<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/controller/user-role-controller.test.ts">
        <testCase name="UserRoleController getConfig Basic functionality should return config with user and riskAssessment properties" duration="2"/>
        <testCase name="UserRoleController getConfig Basic functionality should return user from tokenParsed" duration="1"/>
        <testCase name="UserRoleController getConfig Role-based permissions canCreateNewTemplate permission should return true when user has RA_CREATE_TEMPLATE_ACCESS role" duration="0"/>
        <testCase name="UserRoleController getConfig Role-based permissions canCreateNewTemplate permission should return false when user does not have RA_CREATE_TEMPLATE_ACCESS role" duration="0"/>
        <testCase name="UserRoleController getConfig Role-based permissions canCreateNewTemplate permission should return false when user has no roles" duration="0"/>
        <testCase name="UserRoleController getConfig Role-based permissions hasPermision (view access) should return true when user has RA_VIEW_ACCESS role" duration="0"/>
        <testCase name="UserRoleController getConfig Role-based permissions hasPermision (view access) should return false when user does not have RA_VIEW_ACCESS role" duration="0"/>
        <testCase name="UserRoleController getConfig Role-based permissions hasPermision (view access) should return false when user has no roles" duration="0"/>
        <testCase name="UserRoleController getConfig Role-based permissions Multiple roles scenarios should handle user with both create and view permissions" duration="0"/>
        <testCase name="UserRoleController getConfig Role-based permissions Multiple roles scenarios should handle user with only view permission" duration="0"/>
        <testCase name="UserRoleController getConfig Role-based permissions Multiple roles scenarios should handle user with only create permission" duration="9"/>
        <testCase name="UserRoleController getConfig Role-based permissions Multiple roles scenarios should handle user with multiple unrelated roles" duration="0"/>
        <testCase name="UserRoleController getConfig Edge cases and error handling should handle missing realmAccess" duration="10"/>
        <testCase name="UserRoleController getConfig Edge cases and error handling should handle missing realmAccess.roles" duration="0"/>
        <testCase name="UserRoleController getConfig Edge cases and error handling should handle null/undefined roles array" duration="1"/>
        <testCase name="UserRoleController getConfig Edge cases and error handling should handle empty tokenParsed" duration="0"/>
        <testCase name="UserRoleController getConfig Edge cases and error handling should handle null tokenParsed" duration="0"/>
        <testCase name="UserRoleController getConfig Role constants validation should use correct role constants" duration="2">
            <failure message="Error: expect(received).toBe(expected) // Object.is equality"><![CDATA[Error: expect(received).toBe(expected) // Object.is equality

Expected: "ra|temp|create"
Received: "tmpl|vw"
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/controller/user-role-controller.test.ts:242:53)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at Object.worker (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/testWorker.js:133:12)]]></failure>
        </testCase>
        <testCase name="UserRoleController getConfig Role constants validation should work with actual role constant values" duration="0">
            <failure message="Error: expect(received).toBe(expected) // Object.is equality"><![CDATA[Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/controller/user-role-controller.test.ts:253:60)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at Object.worker (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/testWorker.js:133:12)]]></failure>
        </testCase>
        <testCase name="UserRoleController getConfig Return type validation should return object with correct structure" duration="0"/>
        <testCase name="UserRoleController getConfig Return type validation should return consistent results for same input" duration="1"/>
        <testCase name="UserRoleController getConfig Integration scenarios should work with realistic user data" duration="0"/>
        <testCase name="UserRoleController getConfig Integration scenarios should handle case-sensitive role matching" duration="0">
            <failure message="Error: expect(received).toBe(expected) // Object.is equality"><![CDATA[Error: expect(received).toBe(expected) // Object.is equality

Expected: true
Received: false
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/controller/user-role-controller.test.ts:323:60)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at Object.worker (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/testWorker.js:133:12)]]></failure>
        </testCase>
        <testCase name="UserRoleController Class instantiation should create instance successfully" duration="0"/>
        <testCase name="UserRoleController Class instantiation should create multiple independent instances" duration="0"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/context/DataStoreProvider.test.tsx">
        <testCase name="DataStoreProvider provides context and children" duration="50"/>
        <testCase name="DataStoreProvider allows updating dataStore via setDataStore" duration="11"/>
        <testCase name="DataStoreProvider navigates to /home if hasPermision is false" duration="3">
            <failure message="Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)"><![CDATA[Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: "/home"

Number of calls: 0
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/context/DataStoreProvider.test.tsx:120:26)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at Object.worker (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/testWorker.js:133:12)]]></failure>
        </testCase>
        <testCase name="DataStoreProvider throws if used outside provider" duration="26"/>
        <testCase name="DataStoreProvider memoizes context value" duration="1"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/PreviewTemplateModal.test.tsx">
        <testCase name="PreviewTemplateModal Basic Rendering renders modal with correct structure" duration="168"/>
        <testCase name="PreviewTemplateModal Basic Rendering renders modal with correct props passed to PreviewFormDetails" duration="53"/>
        <testCase name="PreviewTemplateModal Basic Rendering applies correct CSS classes to modal" duration="50"/>
        <testCase name="PreviewTemplateModal Modal Properties renders modal with xl size" duration="9"/>
        <testCase name="PreviewTemplateModal Modal Properties renders modal with static backdrop" duration="23"/>
        <testCase name="PreviewTemplateModal Modal Properties shows modal by default" duration="14"/>
        <testCase name="PreviewTemplateModal Loading States shows loader initially" duration="24"/>
        <testCase name="PreviewTemplateModal Loading States hides loader after data is fetched" duration="21"/>
        <testCase name="PreviewTemplateModal Loading States shows loader during API call" duration="18"/>
        <testCase name="PreviewTemplateModal Data Fetching calls getTemplateById with correct id on mount" duration="31"/>
        <testCase name="PreviewTemplateModal Data Fetching calls createFormFromData with fetched data" duration="15"/>
        <testCase name="PreviewTemplateModal Data Fetching does not fetch data when id is not provided" duration="8"/>
        <testCase name="PreviewTemplateModal Data Fetching refetches data when id changes" duration="11"/>
        <testCase name="PreviewTemplateModal Error Handling handles API error gracefully" duration="11"/>
        <testCase name="PreviewTemplateModal Error Handling still renders form with default data when API fails" duration="19"/>
        <testCase name="PreviewTemplateModal Button Interactions calls onClose when Cancel button is clicked" duration="10"/>
        <testCase name="PreviewTemplateModal Button Interactions navigates to correct route when Use Template button is clicked" duration="10"/>
        <testCase name="PreviewTemplateModal Button Interactions disables Use Template button when loading" duration="19"/>
        <testCase name="PreviewTemplateModal Button Interactions enables Use Template button after loading completes" duration="8"/>
        <testCase name="PreviewTemplateModal canUseTemplate Prop shows Use Template button when canUseTemplate is true" duration="10"/>
        <testCase name="PreviewTemplateModal canUseTemplate Prop hides Use Template button when canUseTemplate is false" duration="6"/>
        <testCase name="PreviewTemplateModal canUseTemplate Prop defaults canUseTemplate to true when not provided" duration="33"/>
        <testCase name="PreviewTemplateModal Form State Management updates form state when setForm is called" duration="18"/>
        <testCase name="PreviewTemplateModal Form State Management initializes form with default data from createFormFromData" duration="7"/>
        <testCase name="PreviewTemplateModal Form State Management updates form with fetched template data" duration="23"/>
        <testCase name="PreviewTemplateModal Modal Header displays correct title" duration="23"/>
        <testCase name="PreviewTemplateModal PreviewFormDetails Integration passes correct props to PreviewFormDetails" duration="8"/>
        <testCase name="PreviewTemplateModal PreviewFormDetails Integration passes atRiskRef as null object" duration="9"/>
        <testCase name="PreviewTemplateModal PreviewFormDetails Integration passes empty functions for handlePreviewPublush and handleSaveToDraft" duration="6"/>
        <testCase name="PreviewTemplateModal Edge Cases handles undefined template data gracefully" duration="12"/>
        <testCase name="PreviewTemplateModal Edge Cases handles null template data gracefully" duration="8"/>
        <testCase name="PreviewTemplateModal Edge Cases handles very large id numbers" duration="9"/>
        <testCase name="PreviewTemplateModal Edge Cases handles negative id numbers" duration="3"/>
        <testCase name="PreviewTemplateModal Edge Cases handles string conversion of id correctly" duration="7"/>
        <testCase name="PreviewTemplateModal User Interactions handles rapid button clicks gracefully" duration="10"/>
        <testCase name="PreviewTemplateModal User Interactions handles keyboard navigation" duration="43"/>
        <testCase name="PreviewTemplateModal User Interactions handles modal close via onHide" duration="18"/>
        <testCase name="PreviewTemplateModal Accessibility has proper ARIA attributes" duration="24"/>
        <testCase name="PreviewTemplateModal Accessibility has focusable elements in correct order" duration="13"/>
        <testCase name="PreviewTemplateModal Accessibility maintains focus within modal" duration="10"/>
        <testCase name="PreviewTemplateModal Performance does not cause unnecessary re-renders" duration="42"/>
        <testCase name="PreviewTemplateModal Performance cleans up properly on unmount" duration="67"/>
        <testCase name="PreviewTemplateModal Function Coverage covers fetchTemplateData function with successful response" duration="39"/>
        <testCase name="PreviewTemplateModal Function Coverage covers fetchTemplateData function with error handling" duration="91"/>
        <testCase name="PreviewTemplateModal Function Coverage covers useEffect dependency array with id change" duration="121"/>
        <testCase name="PreviewTemplateModal Function Coverage covers useEffect early return when id is falsy" duration="17"/>
        <testCase name="PreviewTemplateModal Function Coverage covers all button click handlers" duration="13"/>
        <testCase name="PreviewTemplateModal Function Coverage covers modal onHide handler" duration="9"/>
        <testCase name="PreviewTemplateModal Function Coverage covers setForm function calls" duration="11"/>
        <testCase name="PreviewTemplateModal Function Coverage covers loading state transitions" duration="73"/>
        <testCase name="PreviewTemplateModal Function Coverage covers all conditional rendering paths" duration="23"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/DiscardDraftModal.test.tsx">
        <testCase name="DiscardDraftModal Component Rendering renders modal with correct title and content" duration="121"/>
        <testCase name="DiscardDraftModal Component Rendering renders modal with correct buttons" duration="55"/>
        <testCase name="DiscardDraftModal Component Rendering renders modal with correct attributes" duration="22"/>
        <testCase name="DiscardDraftModal Component Rendering renders buttons with correct classes" duration="37"/>
        <testCase name="DiscardDraftModal Button States enables both buttons initially" duration="51"/>
        <testCase name="DiscardDraftModal Button States disables both buttons when deleting" duration="142"/>
        <testCase name="DiscardDraftModal Cancel Functionality calls onClose without parameters when cancel is clicked" duration="12"/>
        <testCase name="DiscardDraftModal Cancel Functionality does not call delete services when cancel is clicked" duration="16"/>
        <testCase name="DiscardDraftModal Delete Risk Functionality (activeTab !== 2) calls deleteRiskById when activeTab is 1 and discard is clicked" duration="17"/>
        <testCase name="DiscardDraftModal Delete Risk Functionality (activeTab !== 2) calls deleteRiskById when activeTab is 0 and discard is clicked" duration="21"/>
        <testCase name="DiscardDraftModal Delete Risk Functionality (activeTab !== 2) calls deleteRiskById when activeTab is 3 and discard is clicked" duration="17"/>
        <testCase name="DiscardDraftModal Delete Template Functionality (activeTab === 2) calls deleteTemplateById when activeTab is 2 and discard is clicked" duration="26"/>
        <testCase name="DiscardDraftModal Error Handling handles deleteRiskById error and still calls onClose" duration="41"/>
        <testCase name="DiscardDraftModal Error Handling handles deleteTemplateById error and still calls onClose" duration="49"/>
        <testCase name="DiscardDraftModal Error Handling resets isDeleting state after error" duration="239"/>
        <testCase name="DiscardDraftModal Props Validation works with different id values" duration="74"/>
        <testCase name="DiscardDraftModal Props Validation works with id value 0" duration="18"/>
        <testCase name="DiscardDraftModal Props Validation works with negative id values" duration="26"/>
        <testCase name="DiscardDraftModal Modal Behavior renders modal with show prop set to true" duration="10"/>
        <testCase name="DiscardDraftModal Modal Behavior renders modal body with correct class" duration="19"/>
        <testCase name="DiscardDraftModal Modal Behavior renders modal with proper structure" duration="18"/>
        <testCase name="DiscardDraftModal Accessibility has proper alert role for warning message" duration="29"/>
        <testCase name="DiscardDraftModal Accessibility has proper modal structure" duration="59"/>
        <testCase name="DiscardDraftModal Accessibility buttons are focusable" duration="24"/>
        <testCase name="DiscardDraftModal Integration Tests completes full delete risk workflow" duration="29"/>
        <testCase name="DiscardDraftModal Integration Tests completes full delete template workflow" duration="56"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/EditTemplateModal.test.tsx">
        <testCase name="EditTemplateModal Basic Rendering renders modal with correct title" duration="216"/>
        <testCase name="EditTemplateModal Basic Rendering renders with correct modal size for step 1" duration="11"/>
        <testCase name="EditTemplateModal Basic Rendering renders with correct modal size for other steps" duration="9"/>
        <testCase name="EditTemplateModal Step Components renders EditBasicDetailsComp for step 1" duration="22"/>
        <testCase name="EditTemplateModal Step Components renders RaCategoryStep for step 2" duration="6"/>
        <testCase name="EditTemplateModal Step Components renders HazardCategoryStep for step 3" duration="30"/>
        <testCase name="EditTemplateModal Step Components renders AtRiskStep for step 4" duration="13"/>
        <testCase name="EditTemplateModal Step Components renders AddJobsStep for step 5" duration="3"/>
        <testCase name="EditTemplateModal Form Validation - Template Type enables save button when template form is valid and has changes" duration="12"/>
        <testCase name="EditTemplateModal Form Validation - Template Type disables save button when template form is invalid" duration="24"/>
        <testCase name="EditTemplateModal Form Validation - Risk Type enables save button when risk form is valid and has changes" duration="36"/>
        <testCase name="EditTemplateModal Form Validation - Risk Type disables save button when risk form is missing required fields" duration="4"/>
        <testCase name="EditTemplateModal Button Actions calls onClose when Cancel button is clicked" duration="5"/>
        <testCase name="EditTemplateModal Button Actions calls onClose when Save Changes is clicked with valid form" duration="19"/>
        <testCase name="EditTemplateModal Button Actions does not save when form is invalid" duration="26"/>
        <testCase name="EditTemplateModal Job Editing (Step 5) filters template_job to specific job when jobId is provided for step 5" duration="9"/>
        <testCase name="EditTemplateModal Job Editing (Step 5) handles save for step 5 with jobId correctly" duration="40"/>
        <testCase name="EditTemplateModal Change Detection disables save button when no changes are made" duration="99"/>
        <testCase name="EditTemplateModal Change Detection enables save button when changes are detected" duration="87"/>
        <testCase name="EditTemplateModal Modal Properties renders modal with static backdrop" duration="98"/>
        <testCase name="EditTemplateModal Modal Properties applies correct CSS class to modal" duration="14"/>
        <testCase name="EditTemplateModal Modal Properties applies correct CSS class to modal body" duration="21"/>
        <testCase name="EditTemplateModal Edge Cases handles empty template_job array for step 5" duration="31"/>
        <testCase name="EditTemplateModal Edge Cases handles missing jobId for step 5" duration="10"/>
        <testCase name="EditTemplateModal Edge Cases handles form with minimal required fields" duration="39"/>
        <testCase name="EditTemplateModal Validation with Mocked Step Components calls validation on step 2 component" duration="24"/>
        <testCase name="EditTemplateModal Validation with Mocked Step Components renders step components correctly" duration="16"/>
        <testCase name="EditTemplateModal Job Index Handling sets correct job index when jobId is found" duration="28"/>
        <testCase name="EditTemplateModal Validation Edge Cases handles validation when refs are null" duration="16"/>
        <testCase name="EditTemplateModal Validation Edge Cases validates step 2 with valid ref" duration="14"/>
        <testCase name="EditTemplateModal Validation Edge Cases validates step 3 with hazard category ref" duration="4"/>
        <testCase name="EditTemplateModal Validation Edge Cases validates step 4 with at risk ref" duration="3"/>
        <testCase name="EditTemplateModal Validation Edge Cases validates step 5 with add jobs ref" duration="7"/>
        <testCase name="EditTemplateModal Save Step 5 Job Logic saves step 5 job when job is found in template_job array" duration="15"/>
        <testCase name="EditTemplateModal Save Step 5 Job Logic handles save step 5 when job index is not found" duration="8"/>
        <testCase name="EditTemplateModal Risk Form Validation Edge Cases validates risk form with empty date_risk_assessment" duration="9"/>
        <testCase name="EditTemplateModal Risk Form Validation Edge Cases validates risk form with zero vessel_ownership_id" duration="8"/>
        <testCase name="EditTemplateModal Risk Form Validation Edge Cases validates risk form with all valid fields" duration="52"/>
        <testCase name="EditTemplateModal Template Form Validation Edge Cases validates template form with empty task_duration" duration="6"/>
        <testCase name="EditTemplateModal Template Form Validation Edge Cases validates template form with whitespace-only fields" duration="11"/>
        <testCase name="EditTemplateModal Change Detection Edge Cases detects changes for step 5 with specific job comparison" duration="5"/>
        <testCase name="EditTemplateModal Change Detection Edge Cases handles no changes detected for non-step-5 scenarios" duration="7"/>
        <testCase name="EditTemplateModal Validation Return Values handles validation when step refs are not available" duration="4"/>
        <testCase name="EditTemplateModal Save Functionality Edge Cases does not save when validation fails" duration="6"/>
        <testCase name="EditTemplateModal Save Functionality Edge Cases saves successfully for non-step-5 scenarios" duration="13"/>
        <testCase name="EditTemplateModal Job Index Edge Cases handles jobId not found in template_job array" duration="10"/>
        <testCase name="EditTemplateModal Job Index Edge Cases handles empty template_job array with jobId" duration="44"/>
        <testCase name="EditTemplateModal Additional Coverage Tests handles step validation with all step refs returning false" duration="3"/>
        <testCase name="EditTemplateModal Additional Coverage Tests handles step validation with mixed validation results" duration="4"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers all branches in risk form validation" duration="7"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers all branches in template form validation" duration="3"/>
        <testCase name="EditTemplateModal Additional Coverage Tests tests save functionality with step validation passing" duration="3"/>
        <testCase name="EditTemplateModal Additional Coverage Tests tests change detection with different form types" duration="6"/>
        <testCase name="EditTemplateModal Additional Coverage Tests tests job index calculation edge cases" duration="2"/>
        <testCase name="EditTemplateModal Additional Coverage Tests tests validation state changes" duration="5"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers saveStep5Job function with valid job index" duration="19"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers saveStep5Job function with invalid job index" duration="3"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers step 5 save with empty template_job array" duration="4"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers step 5 save without jobId" duration="7"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers validation failure path in handleSave" duration="5"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers else branch in handleSave for non-step-5 scenarios" duration="24"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers else branch in handleSave for step 5 without jobId" duration="9"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers else branch in handleSave for step 5 with empty template_job" duration="2"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers validation with all step refs undefined" duration="4"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers all validation branches for different steps" duration="5"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers risk form validation with all edge cases" duration="7"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers template form validation with all edge cases" duration="3"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers validateStep return false for invalid steps" duration="2"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers saveStep5Job with job found and updated" duration="3"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers saveStep5Job with job not found (idx === -1)" duration="5"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers line 174 - step 5 with jobId condition in handleSave" duration="3"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers validateStep return false for steps without refs" duration="4"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers validateStep for step 3 without ref" duration="6"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers validateStep for step 4 without ref" duration="9"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers validateStep for step 5 without ref" duration="2"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers validateStep return false for invalid step numbers" duration="4"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers validateStep line 150 - return false case" duration="2"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers saveStep5Job with risk form and valid jobId" duration="7"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers saveStep5Job with template form and valid jobId" duration="50"/>
        <testCase name="EditTemplateModal Additional Coverage Tests covers error case when jobId is required but not provided for risk form" duration="8"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/InitialRiskRatingModal.test.tsx">
        <testCase name="InitialRiskRatingModal does not render modal when `show` is false" duration="65"/>
        <testCase name="InitialRiskRatingModal calls onHide when Cancel button is clicked" duration="322"/>
        <testCase name="InitialRiskRatingModal calls onSelect when a tile is clicked" duration="68"/>
        <testCase name="InitialRiskRatingModal highlights selected tile based on selectedValue" duration="51"/>
        <testCase name="InitialRiskRatingModal renders modal with custom title" duration="42"/>
        <testCase name="InitialRiskRatingModal renders modal without title when title prop is not provided" duration="67"/>
        <testCase name="InitialRiskRatingModal renders all consequence rows and likelihood columns" duration="83"/>
        <testCase name="InitialRiskRatingModal renders all risk code tiles" duration="74"/>
        <testCase name="InitialRiskRatingModal updates selected state when selectedValue prop changes" duration="58"/>
        <testCase name="InitialRiskRatingModal handles empty selectedValue prop" duration="348"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality disables tiles with higher risk than irrValue" duration="166"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality tests compareRiskCodes function through different irrValue scenarios" duration="146"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality tests compareRiskCodes with same row different columns" duration="27"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality tests compareRiskCodes with different rows same columns" duration="62"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality handles edge case with no irrValue" duration="103"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality handles empty irrValue" duration="63"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality shows correct tooltip for disabled tiles" duration="32"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality does not call onSelect when disabled tile is clicked" duration="23"/>
        <testCase name="InitialRiskRatingModal Disabled tiles functionality shows tooltip for significant risk reduction" duration="46"/>
        <testCase name="InitialRiskRatingModal Utility functions getCellColor returns green color for green codes" duration="3"/>
        <testCase name="InitialRiskRatingModal Utility functions getCellColor returns red color for red codes" duration="0"/>
        <testCase name="InitialRiskRatingModal Utility functions getCellColor returns yellow color for other codes" duration="0"/>
        <testCase name="InitialRiskRatingModal Utility functions Edge cases and error handling handles invalid risk codes gracefully" duration="31"/>
        <testCase name="InitialRiskRatingModal Utility functions Edge cases and error handling handles malformed risk codes in comparison" duration="103"/>
        <testCase name="InitialRiskRatingModal Component behavior calls onSelect without onSelect prop" duration="32"/>
        <testCase name="InitialRiskRatingModal Component behavior renders correct tile colors based on getCellColor" duration="39"/>
        <testCase name="InitialRiskRatingModal Component behavior handles modal close via backdrop or escape" duration="56"/>
        <testCase name="InitialRiskRatingModal Component behavior updates internal state when tile is clicked" duration="100"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/DropdownTypeahead.test.tsx">
        <testCase name="DropdownTypeahead renders with proper accessibility attributes" duration="207"/>
        <testCase name="DropdownTypeahead hides label when hideLabel prop is true" duration="7"/>
        <testCase name="DropdownTypeahead disables the input when disabled prop is true" duration="20"/>
        <testCase name="DropdownTypeahead shows error message when isInvalid is true" duration="14"/>
        <testCase name="DropdownTypeahead handles single selection correctly" duration="186"/>
        <testCase name="DropdownTypeahead handles multiple selection correctly" duration="142"/>
        <testCase name="DropdownTypeahead calls onInputChange when typing in the input" duration="72"/>
        <testCase name="DropdownTypeahead displays &quot;No results found&quot; when no options match search" duration="383"/>
        <testCase name="DropdownTypeahead handles special option selection" duration="74"/>
        <testCase name="DropdownTypeahead displays token with more count for multiple selection" duration="22"/>
        <testCase name="DropdownTypeahead clears selection when clear button is clicked" duration="27"/>
        <testCase name="DropdownTypeahead toggles dropdown when clear icon is clicked with no selection" duration="33"/>
        <testCase name="DropdownTypeahead renders custom placeholder when disabledSelectPrefix is true" duration="13"/>
        <testCase name="DropdownTypeahead shows required indicator when required prop is true" duration="61"/>
        <testCase name="DropdownTypeahead handles non-object options correctly" duration="78"/>
        <testCase name="DropdownTypeahead shows danger icon when invalid and multiple with no selection" duration="10"/>
        <testCase name="DropdownTypeahead shows cross icon when there is a selected value" duration="15"/>
        <testCase name="DropdownTypeahead handles keyboard navigation on wrapper" duration="24"/>
        <testCase name="DropdownTypeahead handles onBlur callback when menu closes after user interaction" duration="77"/>
        <testCase name="DropdownTypeahead handles options with different key properties" duration="29"/>
        <testCase name="DropdownTypeahead handles tooltip titles for options" duration="35"/>
        <testCase name="DropdownTypeahead handles menu toggle state correctly" duration="28"/>
        <testCase name="DropdownTypeahead handles input focus and keydown events" duration="26"/>
        <testCase name="DropdownTypeahead renders with correct CSS classes for invalid state" duration="10"/>
        <testCase name="DropdownTypeahead handles empty option list with special option" duration="98"/>
        <testCase name="DropdownTypeahead handles keyboard navigation with Enter and Space keys" duration="17"/>
        <testCase name="DropdownTypeahead handles onBlur callback prop" duration="13"/>
        <testCase name="DropdownTypeahead handles useCheckboxes with multiple selection correctly" duration="34"/>
        <testCase name="DropdownTypeahead handles disabledSelectPrefix prop correctly" duration="47"/>
        <testCase name="DropdownTypeahead handles onInputChange callback" duration="145"/>
        <testCase name="DropdownTypeahead renders with correct styling when invalid and multiple" duration="10"/>
        <testCase name="DropdownTypeahead handles filterBy function when useCheckboxes is true" duration="91"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RAListing/RAListing.test.tsx">
        <testCase name="RAListing renders the main UI elements" duration="28"/>
        <testCase name="RAListing navigates to template listing on button click" duration="6"/>
        <testCase name="RAListing navigates to drafts on button click" duration="2"/>
        <testCase name="RAListing handles filter change" duration="10"/>
        <testCase name="RAListing shows table data" duration="2"/>
        <testCase name="RAListing navigates to template selection from dropdown" duration="222"/>
        <testCase name="RAListing navigates to template creation from dropdown" duration="75"/>
        <testCase name="RAListing handles create RA without template dropdown option" duration="125"/>
        <testCase name="RAListing sorts by default when sorting is cleared" duration="7"/>
        <testCase name="RAListing renders all columns in the table" duration="2"/>
        <testCase name="RAListing renders Comments and Action columns cell renderers directly for coverage" duration="2"/>
        <testCase name="RAListing renders Vessel/Office Name column with only office_name" duration="2"/>
        <testCase name="RAListing renders Vessel/Office Name column with vessel name as link" duration="1"/>
        <testCase name="RAListing renders Vessel/Office Name column with only vessel name as link" duration="1"/>
        <testCase name="RAListing renders Level of RA column with null value" duration="1"/>
        <testCase name="RAListing renders Tech Group column with string value" duration="1"/>
        <testCase name="RAListing renders Vessel Category column with string value" duration="1"/>
        <testCase name="RAListing renders Vessel Category column with null value" duration="0"/>
        <testCase name="RAListing renders Assessor column with valid assessor id" duration="0"/>
        <testCase name="RAListing renders Assessor column with invalid assessor id" duration="1"/>
        <testCase name="RAListing renders Assessor column with null value" duration="0"/>
        <testCase name="RAListing renders Submitted on column with date" duration="0"/>
        <testCase name="RAListing renders Date of Risk Assessment column with date" duration="0"/>
        <testCase name="RAListing renders Comments column with null value" duration="1"/>
        <testCase name="RAListing renders Action column with id" duration="1"/>
        <testCase name="RAListing handles sorting ASC branch" duration="9"/>
        <testCase name="RAListing handles empty data gracefully" duration="11"/>
        <testCase name="RAListing renders Task Required column with TruncateText" duration="2"/>
        <testCase name="RAListing renders Task Required column with null value" duration="1"/>
        <testCase name="RAListing renders Vessel/Office Name column with null values" duration="3"/>
        <testCase name="RAListing renders Level of RA column with valid number" duration="7"/>
        <testCase name="RAListing renders Tech Group column with null value" duration="2"/>
        <testCase name="RAListing renders Submitted on column with null date" duration="2"/>
        <testCase name="RAListing renders Approval Date column with undefined date" duration="1"/>
        <testCase name="RAListing renders Date of Risk Assessment column with null date" duration="0"/>
        <testCase name="RAListing handles filter change with different filter types" duration="4"/>
        <testCase name="RAListing handles sorting with undefined desc value" duration="7"/>
        <testCase name="RAListing tests filtersAndSorters object construction with various filter values" duration="2"/>
        <testCase name="RAListing tests sort_order logic with boolean desc value" duration="4"/>
        <testCase name="RAListing handles loading state" duration="2"/>
        <testCase name="RAListing handles fetchingNextPage state" duration="2"/>
        <testCase name="RAListing handles complex filter scenarios" duration="5"/>
        <testCase name="RAListing tests all column configurations and properties" duration="16"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/CreateRA/CreateRA.page.test.tsx">
        <testCase name="StepperPage Component Component Rendering renders the component with GenericStepper initially" duration="436"/>
        <testCase name="StepperPage Component Component Rendering renders the first step component correctly" duration="4"/>
        <testCase name="StepperPage Component Component Rendering does not render preview initially" duration="6"/>
        <testCase name="StepperPage Component Component Rendering does not render confirm publish modal initially" duration="8"/>
        <testCase name="StepperPage Component Data Loading loads all required data on component mount" duration="31"/>
        <testCase name="StepperPage Component Data Loading calls setDataStore with loaded data" duration="40"/>
        <testCase name="StepperPage Component Data Loading handles data loading errors gracefully" duration="57"/>
        <testCase name="StepperPage Component Data Loading processes risk parameter data with lodash groupBy" duration="7"/>
        <testCase name="StepperPage Component Data Loading calls getMainRiskParameterType twice with different parameters" duration="19"/>
        <testCase name="StepperPage Component Data Loading handles partial service failures" duration="8"/>
        <testCase name="StepperPage Component Navigation and Button Interactions handles close button click for template creation" duration="72"/>
        <testCase name="StepperPage Component Navigation and Button Interactions handles close button click for risk creation" duration="13"/>
        <testCase name="StepperPage Component Navigation and Button Interactions handles close button click for draft editing" duration="6"/>
        <testCase name="StepperPage Component Navigation and Button Interactions displays correct button titles" duration="6"/>
        <testCase name="StepperPage Component Navigation and Button Interactions displays Preview Template on last step" duration="30"/>
        <testCase name="StepperPage Component Preview Functionality primary button is initially disabled" duration="42"/>
        <testCase name="StepperPage Component Preview Functionality enables primary button after validation" duration="21"/>
        <testCase name="StepperPage Component Preview Functionality shows success toast when handleSave is called during preview" duration="13"/>
        <testCase name="StepperPage Component Preview Functionality calls handlePreview when primary button is clicked" duration="8"/>
        <testCase name="StepperPage Component Preview Functionality handles error in handleSave" duration="6"/>
        <testCase name="StepperPage Component Modal Functionality tests modal components are properly mocked" duration="8"/>
        <testCase name="StepperPage Component Modal Functionality shows preview modal when openPreview is true" duration="10"/>
        <testCase name="StepperPage Component Modal Functionality shows confirm publish modal when handlePreviewPublush is called" duration="4"/>
        <testCase name="StepperPage Component Modal Functionality closes confirm publish modal when onClose is called" duration="5"/>
        <testCase name="StepperPage Component Modal Functionality handles onSave in confirm publish modal" duration="15"/>
        <testCase name="StepperPage Component Step Validation handles next step validation for BasicDetails (step 1)" duration="7"/>
        <testCase name="StepperPage Component Step Validation handles step change validation" duration="6"/>
        <testCase name="StepperPage Component Step Validation validates BasicDetails step (step 1) with successful validation" duration="13"/>
        <testCase name="StepperPage Component Step Validation validates BasicDetails step (step 1) with failed validation" duration="9"/>
        <testCase name="StepperPage Component Step Validation validates RaCategoryStep (step 2)" duration="11"/>
        <testCase name="StepperPage Component Step Validation validates HazardCategoryStep (step 3)" duration="7"/>
        <testCase name="StepperPage Component Step Validation validates other steps (step 4+)" duration="11"/>
        <testCase name="StepperPage Component Form State Management initializes form with default values" duration="14"/>
        <testCase name="StepperPage Component Form State Management renders step components indicating form state is passed" duration="9"/>
        <testCase name="StepperPage Component Form State Management initializes keywords state as empty array" duration="5"/>
        <testCase name="StepperPage Component Form State Management initializes stepValid state as false" duration="6"/>
        <testCase name="StepperPage Component Form State Management initializes openPreview state as false" duration="14"/>
        <testCase name="StepperPage Component Form State Management initializes showConfirmPublishDetailsModal state as false" duration="5"/>
        <testCase name="StepperPage Component Form State Management maintains form structure with all required fields" duration="5"/>
        <testCase name="StepperPage Component Error Handling handles service errors during data loading" duration="11"/>
        <testCase name="StepperPage Component Error Handling logs errors to console during data loading" duration="4"/>
        <testCase name="StepperPage Component Component Integration passes correct props to GenericStepper" duration="3"/>
        <testCase name="StepperPage Component Component Integration renders step components indicating proper integration" duration="4"/>
        <testCase name="StepperPage Component Component Integration handles primary button disabled state correctly" duration="5"/>
        <testCase name="StepperPage Component Component Integration has all required step components configured" duration="9"/>
        <testCase name="StepperPage Component Component Integration configures secondary button correctly" duration="6"/>
        <testCase name="StepperPage Component Edge Cases and Error Boundaries handles empty data responses gracefully" duration="5"/>
        <testCase name="StepperPage Component Edge Cases and Error Boundaries handles null/undefined data responses" duration="5"/>
        <testCase name="StepperPage Component Edge Cases and Error Boundaries handles component unmounting during async operations" duration="1"/>
        <testCase name="StepperPage Component Edge Cases and Error Boundaries handles multiple rapid button clicks" duration="7"/>
        <testCase name="StepperPage Component Edge Cases and Error Boundaries handles step validation with missing refs" duration="4"/>
        <testCase name="StepperPage Component Edge Cases and Error Boundaries handles form state updates during validation" duration="5"/>
        <testCase name="StepperPage Component Edge Cases and Error Boundaries handles concurrent API calls" duration="156"/>
        <testCase name="StepperPage Component StepperPage Additional Coverage shows loading spinner when loading" duration="21"/>
        <testCase name="StepperPage Component StepperPage Additional Coverage handles error in handelFormPublish" duration="4"/>
        <testCase name="StepperPage Component StepperPage Additional Coverage handles error in handleSaveToDraft" duration="7"/>
        <testCase name="StepperPage Component StepperPage Additional Coverage handles pathname edge cases (no match)" duration="5"/>
        <testCase name="StepperPage Component StepperPage Additional Coverage handles pathname edge cases (risk-creation)" duration="4"/>
        <testCase name="StepperPage Component Advanced Coverage Tests tests fetchAndSetTemplateData with defaultTemplateId path" duration="3"/>
        <testCase name="StepperPage Component Advanced Coverage Tests tests fetchAndSetTemplateData with draft_step logic" duration="6"/>
        <testCase name="StepperPage Component Advanced Coverage Tests tests fetchAndSetTemplateData with preview path" duration="5"/>
        <testCase name="StepperPage Component Advanced Coverage Tests tests fetchAndSetTemplateData error handling" duration="3"/>
        <testCase name="StepperPage Component Advanced Coverage Tests tests different pathname patterns for type detection" duration="8"/>
        <testCase name="StepperPage Component Advanced Coverage Tests tests loading state management" duration="6"/>
        <testCase name="StepperPage Component Helper Functions Coverage tests getPrimaryBtnTitle for different scenarios" duration="0"/>
        <testCase name="StepperPage Component Helper Functions Coverage tests getTypeAndIds with various pathname patterns" duration="1"/>
        <testCase name="StepperPage Component Additional Coverage Tests tests error handling in save functionality" duration="55"/>
        <testCase name="StepperPage Component Additional Coverage Tests tests different step validation scenarios" duration="9"/>
        <testCase name="StepperPage Component Additional Coverage Tests tests form state updates" duration="5"/>
        <testCase name="StepperPage Component Additional Coverage Tests tests navigation scenarios" duration="9"/>
        <testCase name="StepperPage Component Additional Coverage Tests tests step change functionality" duration="4"/>
        <testCase name="StepperPage Component Advanced Branch Coverage Tests tests updateUrlAndForm with no result.id" duration="12"/>
        <testCase name="StepperPage Component Advanced Branch Coverage Tests tests updateUrlAndForm with result.id for template" duration="55"/>
        <testCase name="StepperPage Component Advanced Branch Coverage Tests tests buildDraftPayload vessel_id deletion" duration="8"/>
        <testCase name="StepperPage Component Advanced Branch Coverage Tests tests error handling in save operations" duration="59"/>
        <testCase name="StepperPage Component Additional Functional Tests tests form payload with vessel_id removal" duration="10"/>
        <testCase name="StepperPage Component Additional Functional Tests tests component state initialization" duration="5"/>
        <testCase name="StepperPage Component Additional Functional Tests tests different type detection scenarios" duration="12"/>
        <testCase name="StepperPage Component Additional Functional Tests tests button state management" duration="6"/>
        <testCase name="StepperPage Component Additional Functional Tests tests component cleanup and error boundaries" duration="4"/>
        <testCase name="StepperPage Component 90% Coverage Target Tests tests risk creation with defaultTemplateId query param" duration="59"/>
        <testCase name="StepperPage Component 90% Coverage Target Tests tests different pathname scenarios for coverage" duration="37"/>
        <testCase name="StepperPage Component 90% Coverage Target Tests tests basic save functionality for different scenarios" duration="89"/>
        <testCase name="StepperPage Component 90% Coverage Target Tests tests save functionality with query parameters" duration="60"/>
        <testCase name="StepperPage Component 90% Coverage Target Tests tests risk creation save functionality" duration="59"/>
        <testCase name="StepperPage Component 90% Coverage Target Tests tests risk creation with templateFormID" duration="70"/>
        <testCase name="StepperPage Component 90% Coverage Target Tests tests component state and navigation scenarios" duration="5"/>
        <testCase name="StepperPage Component 90% Coverage Target Tests tests specific uncovered line coverage - buildDraftPayload with template_id" duration="61"/>
        <testCase name="StepperPage Component 90% Coverage Target Tests tests specific uncovered line coverage - updateDraft risk path" duration="58"/>
        <testCase name="StepperPage Component 90% Coverage Target Tests tests specific uncovered line coverage - createDraft risk path" duration="60"/>
        <testCase name="StepperPage Component 90% Coverage Target Tests tests specific uncovered line coverage - navigation when currentStep &gt; steps.length" duration="6"/>
        <testCase name="StepperPage Component 90% Coverage Target Tests tests specific uncovered line coverage - handlePreviewPublush modal logic" duration="5"/>
        <testCase name="StepperPage Component Additional Coverage for 90% Target covers line 231-232: setLoadStep and validateStep when defaultTemplateId exists" duration="5"/>
        <testCase name="StepperPage Component Additional Coverage for 90% Target covers line 283: error handling in fetchAndSetTemplateData" duration="6"/>
        <testCase name="StepperPage Component Additional Coverage for 90% Target covers line 368-369: validation failure path" duration="6"/>
        <testCase name="StepperPage Component Additional Coverage for 90% Target covers line 409: vessel_id deletion in buildDraftPayload" duration="3"/>
        <testCase name="StepperPage Component Additional Coverage for 90% Target covers line 473: navigation when currentStep &gt; steps.length" duration="10"/>
        <testCase name="StepperPage Component Additional Coverage for 90% Target covers lines 486-517: handelFormPublish function" duration="5"/>
        <testCase name="StepperPage Component Additional Coverage for 90% Target covers lines 579-590: handlePreviewPublush for risk type" duration="4"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers lines 231-232: setLoadStep and validateStep when defaultTemplateId exists" duration="5"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers lines 368-369: validation failure when ref.current.validate() returns false" duration="5"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers line 409: vessel_id deletion when vessel_id is falsy" duration="12"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers line 473: navigation when currentStep &gt; steps.length" duration="4"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers lines 486-517: handelFormPublish function execution" duration="6"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers lines 579-590: handlePreviewPublush for risk type calling handelFormPublish" duration="5"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers defaultTemplateId logic with risk creation from template" duration="86"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers buildDraftPayload with defaultTemplateId and risk type" duration="28"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers lines 231-232: fetchAndSetTemplateData with defaultTemplateId path" duration="5"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers lines 368-369: validation failure path in validateStep" duration="7"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers line 409: vessel_id deletion in buildDraftPayload" duration="73"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers line 473: navigation when currentStep &gt; steps.length" duration="67"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers lines 486-517: handelFormPublish function complete execution" duration="5"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers lines 579-590: handlePreviewPublush for risk type" duration="8"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers specific line 231-232: setLoadStep and validateStep with defaultTemplateId" duration="11"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers specific line 368-369: validation failure in validateStep" duration="6"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers specific line 409: vessel_id deletion when falsy" duration="12"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers specific line 473: navigation when currentStep &gt; steps.length" duration="20"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers lines 486-517: complete handelFormPublish execution" duration="7"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers lines 579-590: risk type handlePreviewPublush direct call" duration="5"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers lines 231-232: defaultTemplateId path in fetchAndSetTemplateData" duration="8"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers lines 231-232: defaultTemplateId path with draft_step" duration="3"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers line 473: navigation when currentStep exceeds steps length" duration="15"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers lines 368-369: validation failure in validateStep" duration="4"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers lines 486-517: handelFormPublish complete flow" duration="7"/>
        <testCase name="StepperPage Component Advanced Coverage Tests for 90%+ covers lines 579-590: risk type handelFormPublish flow" duration="7"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/EditBasicDetail.test.tsx">
        <testCase name="EditBasicDetailsComp Template Form Rendering renders basic template form fields" duration="39"/>
        <testCase name="EditBasicDetailsComp Template Form Rendering handles task requiring RA input changes" duration="95"/>
        <testCase name="EditBasicDetailsComp Template Form Rendering handles task duration input changes" duration="3"/>
        <testCase name="EditBasicDetailsComp Template Form Rendering shows validation errors for empty required fields" duration="4"/>
        <testCase name="EditBasicDetailsComp Template Form Rendering enforces maxLength on task requiring RA field" duration="3"/>
        <testCase name="EditBasicDetailsComp Template Form Rendering enforces maxLength on task duration field" duration="4"/>
        <testCase name="EditBasicDetailsComp Template Form Rendering shows placeholder text for task duration" duration="5"/>
        <testCase name="EditBasicDetailsComp Template Form Rendering shows helper text for task duration" duration="3"/>
        <testCase name="EditBasicDetailsComp Risk Form Rendering renders risk form with additional fields" duration="15"/>
        <testCase name="EditBasicDetailsComp Risk Form Rendering handles assessor dropdown changes" duration="14"/>
        <testCase name="EditBasicDetailsComp Risk Form Rendering handles vessel/office dropdown changes" duration="9"/>
        <testCase name="EditBasicDetailsComp Risk Form Rendering handles date changes" duration="11"/>
        <testCase name="EditBasicDetailsComp Risk Form Rendering handles approval required changes for office assessor" duration="4"/>
        <testCase name="EditBasicDetailsComp Risk Form Rendering handles approval required changes for vessel assessor" duration="8"/>
        <testCase name="EditBasicDetailsComp Risk Form Rendering shows validation errors for empty risk form fields" duration="12"/>
        <testCase name="EditBasicDetailsComp Risk Form Rendering loads vessel and office options on mount for risk type" duration="10"/>
        <testCase name="EditBasicDetailsComp Error Handling handles errors in loading vessel/office options gracefully" duration="5"/>
        <testCase name="EditBasicDetailsComp Error Handling handles missing data store values gracefully" duration="58"/>
        <testCase name="EditBasicDetailsComp Edge Cases handles undefined clonedForm values" duration="4"/>
        <testCase name="EditBasicDetailsComp Edge Cases handles null vessel_ownership_id in risk form" duration="67"/>
        <testCase name="EditBasicDetailsComp Edge Cases handles empty approval_required array" duration="8"/>
        <testCase name="EditBasicDetailsComp Edge Cases handles switching between assessor types" duration="38"/>
        <testCase name="EditBasicDetailsComp Edge Cases handles template type with default props" duration="2"/>
        <testCase name="EditBasicDetailsComp Accessibility has proper form labels and controls" duration="1"/>
        <testCase name="EditBasicDetailsComp Accessibility shows proper error messages with invalid feedback" duration="1"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RATemplateListing/components/ArchiveTemplateModal.test.tsx">
        <testCase name="ArchiveTemplateModal Component Rendering renders the trigger element" duration="21"/>
        <testCase name="ArchiveTemplateModal Component Rendering does not show modal initially" duration="19"/>
        <testCase name="ArchiveTemplateModal Component Rendering renders with custom trigger element" duration="4"/>
        <testCase name="ArchiveTemplateModal Modal Functionality opens modal when trigger is clicked" duration="159"/>
        <testCase name="ArchiveTemplateModal Modal Functionality closes modal when Cancel button is clicked" duration="77"/>
        <testCase name="ArchiveTemplateModal Modal Functionality closes modal when clicking outside (onHide)" duration="27"/>
        <testCase name="ArchiveTemplateModal Template Details Display displays template name correctly" duration="16"/>
        <testCase name="ArchiveTemplateModal Template Details Display displays risk categories count" duration="18"/>
        <testCase name="ArchiveTemplateModal Template Details Display displays hazard categories count" duration="32"/>
        <testCase name="ArchiveTemplateModal Template Details Display displays created on date" duration="130"/>
        <testCase name="ArchiveTemplateModal Template Details Display displays keywords through SingleBadgePopover" duration="49"/>
        <testCase name="ArchiveTemplateModal Archive Functionality calls markTemplateAsArchived service when Move to Archive button is clicked" duration="59"/>
        <testCase name="ArchiveTemplateModal Archive Functionality closes modal after successful archiving" duration="45"/>
        <testCase name="ArchiveTemplateModal Archive Functionality calls onSuccess callback after successful archiving" duration="31"/>
        <testCase name="ArchiveTemplateModal Archive Functionality handles archiving error gracefully" duration="97"/>
        <testCase name="ArchiveTemplateModal Props Variations handles empty keywords array" duration="25"/>
        <testCase name="ArchiveTemplateModal Props Variations handles single keyword" duration="17"/>
        <testCase name="ArchiveTemplateModal Props Variations handles zero risk and hazard categories" duration="17"/>
        <testCase name="ArchiveTemplateModal Props Variations handles long template name" duration="18"/>
        <testCase name="ArchiveTemplateModal Props Variations handles special characters in template name" duration="18"/>
        <testCase name="ArchiveTemplateModal Props Variations handles different date formats" duration="15"/>
        <testCase name="ArchiveTemplateModal Modal Structure and Classes applies correct CSS classes to modal" duration="17"/>
        <testCase name="ArchiveTemplateModal Modal Structure and Classes renders modal header correctly" duration="19"/>
        <testCase name="ArchiveTemplateModal Modal Structure and Classes renders alert message with correct styling" duration="16"/>
        <testCase name="ArchiveTemplateModal Modal Structure and Classes renders template detail card structure" duration="24"/>
        <testCase name="ArchiveTemplateModal Modal Structure and Classes renders button group with correct variants" duration="16"/>
        <testCase name="ArchiveTemplateModal Accessibility modal is centered" duration="35"/>
        <testCase name="ArchiveTemplateModal Accessibility has proper modal structure for screen readers" duration="126"/>
        <testCase name="ArchiveTemplateModal Edge Cases handles rapid clicking of trigger" duration="64"/>
        <testCase name="ArchiveTemplateModal Edge Cases handles clicking archive button multiple times" duration="77"/>
        <testCase name="ArchiveTemplateModal Edge Cases handles multiple archive button clicks by closing modal" duration="51"/>
        <testCase name="ArchiveTemplateModal Edge Cases shows loading state during archive operation" duration="155"/>
        <testCase name="ArchiveTemplateModal Edge Cases prevents multiple simultaneous archive operations" duration="103"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/CreateRA/PreviewFormDetails.test.tsx">
        <testCase name="PreviewFormDetails Component Component Rendering renders the component with all main sections" duration="209"/>
        <testCase name="PreviewFormDetails Component Component Rendering displays task title correctly" duration="35"/>
        <testCase name="PreviewFormDetails Component Component Rendering displays task duration correctly" duration="40"/>
        <testCase name="PreviewFormDetails Component Component Rendering displays default task title when not provided" duration="24"/>
        <testCase name="PreviewFormDetails Component Component Rendering displays dash when task duration is not provided" duration="19"/>
        <testCase name="PreviewFormDetails Component Component Rendering renders infinite scroll table with correct data" duration="23"/>
        <testCase name="PreviewFormDetails Component Component Rendering renders table with empty data when no jobs" duration="52"/>
        <testCase name="PreviewFormDetails Component Breadcrumb Navigation renders breadcrumb with correct items" duration="42"/>
        <testCase name="PreviewFormDetails Component Breadcrumb Navigation renders empty breadcrumb item when task title is empty" duration="12"/>
        <testCase name="PreviewFormDetails Component Form Input Interactions handles alternative consideration textarea change" duration="27"/>
        <testCase name="PreviewFormDetails Component Form Input Interactions handles rejection reason textarea change" duration="36"/>
        <testCase name="PreviewFormDetails Component Form Input Interactions displays current values in textareas" duration="17"/>
        <testCase name="PreviewFormDetails Component Button Interactions calls handlePreviewPublish when Publish Template button is clicked" duration="76"/>
        <testCase name="PreviewFormDetails Component Button Interactions calls handleSaveToDraft when Save to Draft button is clicked" duration="30"/>
        <testCase name="PreviewFormDetails Component Button Interactions opens guidance PDF when Guidance Table button is clicked" duration="19"/>
        <testCase name="PreviewFormDetails Component Button Interactions opens risk matrix PDF when Risk Matrix Table button is clicked" duration="43"/>
        <testCase name="PreviewFormDetails Component Button Interactions opens guidance table PDF when button is clicked" duration="8"/>
        <testCase name="PreviewFormDetails Component Button Interactions opens risk matrix PDF when button is clicked" duration="21"/>
        <testCase name="PreviewFormDetails Component Category and Hazard Display displays risk categories as badges" duration="23"/>
        <testCase name="PreviewFormDetails Component Category and Hazard Display handles categories with is_other flag" duration="57"/>
        <testCase name="PreviewFormDetails Component Category and Hazard Display handles hazards with is_other flag" duration="7"/>
        <testCase name="PreviewFormDetails Component Category and Hazard Display displays empty categories when no categories selected" duration="10"/>
        <testCase name="PreviewFormDetails Component At Risk Parameters Display displays parameter names in uppercase" duration="17"/>
        <testCase name="PreviewFormDetails Component At Risk Parameters Display handles parameters with custom values" duration="7"/>
        <testCase name="PreviewFormDetails Component At Risk Parameters Display handles empty parameters array" duration="23"/>
        <testCase name="PreviewFormDetails Component Table Configuration renders table with correct column count" duration="14"/>
        <testCase name="PreviewFormDetails Component Table Configuration displays action menu icon in table" duration="15"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling handles undefined form properties gracefully" duration="10"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling handles empty data store gracefully" duration="223"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling handles missing dataStore properties" duration="12"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling displays user name correctly when user is creator/updater" duration="35"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling displays --- when user is not creator/updater" duration="15"/>
        <testCase name="PreviewFormDetails Component Edge Cases and Error Handling renders edit buttons with correct icons" duration="13"/>
        <testCase name="PreviewFormDetails Component Component Integration passes correct props to RiskRatingStep" duration="5"/>
        <testCase name="PreviewFormDetails Component Component Integration passes correct props to BottomButton" duration="5"/>
        <testCase name="PreviewFormDetails Component Component Integration renders input components with correct labels" duration="13"/>
        <testCase name="PreviewFormDetails Component Component Integration hides action buttons when previewOnly is true" duration="12"/>
        <testCase name="PreviewFormDetails Component Component Integration shows correct button text for risk type" duration="0"/>
        <testCase name="PreviewFormDetails Component Styling and Layout applies correct styling to main sections" duration="9"/>
        <testCase name="PreviewFormDetails Component Styling and Layout renders badges with correct styling attributes" duration="9"/>
        <testCase name="PreviewFormDetails Component Additional Component Functionality handles different form configurations" duration="11"/>
        <testCase name="PreviewFormDetails Component Additional Component Functionality handles empty arrays in form data" duration="6"/>
        <testCase name="PreviewFormDetails Component Additional Component Functionality handles button interactions" duration="11"/>
        <testCase name="PreviewFormDetails Component Additional Component Functionality renders with preview mode correctly" duration="6"/>
        <testCase name="PreviewFormDetails Component Additional Component Functionality handles edit hazard categories button click" duration="31"/>
        <testCase name="PreviewFormDetails Component Additional Component Functionality handles edit at-risk parameters button click" duration="24"/>
        <testCase name="PreviewFormDetails Component Additional Component Functionality handles add job modal close" duration="53"/>
        <testCase name="PreviewFormDetails Component Edit Functionality renders edit basic details button and handles click" duration="37"/>
        <testCase name="PreviewFormDetails Component Edit Functionality renders edit category buttons" duration="13"/>
        <testCase name="PreviewFormDetails Component Edit Functionality renders table and maintains state after interactions" duration="7"/>
        <testCase name="PreviewFormDetails Component Modal Interactions renders add job button and handles click" duration="18"/>
        <testCase name="PreviewFormDetails Component Modal Interactions renders table with action menu" duration="10"/>
        <testCase name="PreviewFormDetails Component Modal Interactions renders guidance and risk matrix buttons" duration="11"/>
        <testCase name="PreviewFormDetails Component Modal Close Functions Coverage covers onEditClose function with editStep === 5" duration="12"/>
        <testCase name="PreviewFormDetails Component Modal Close Functions Coverage covers onDeleteClose function" duration="10"/>
        <testCase name="PreviewFormDetails Component Modal Close Functions Coverage covers setShowAddJobModal close function" duration="12"/>
        <testCase name="PreviewFormDetails Component Risk Form Specific Coverage handles approval_required with matching options" duration="7"/>
        <testCase name="PreviewFormDetails Component Risk Form Specific Coverage handles approval_required fallback to count display" duration="21"/>
        <testCase name="PreviewFormDetails Component Risk Form Specific Coverage handles different assessor values" duration="37"/>
        <testCase name="PreviewFormDetails Component Specific Line Coverage Tests covers edit at-risk parameters button click (line 674-676)" duration="11"/>
        <testCase name="PreviewFormDetails Component Specific Line Coverage Tests covers save to draft with risk type (line 799)" duration="5"/>
        <testCase name="PreviewFormDetails Component Specific Line Coverage Tests covers button text for risk type (line 803)" duration="6"/>
        <testCase name="PreviewFormDetails Component Specific Line Coverage Tests covers approval required length display (line 396)" duration="161"/>
        <testCase name="PreviewFormDetails Component Specific Line Coverage Tests covers lines 184-186 with specific form configuration" duration="13"/>
        <testCase name="PreviewFormDetails Component Specific Line Coverage Tests covers modal close functions by simulating component state changes" duration="8"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers lines 184-186 with null/empty values" duration="13"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers modal close functions with EditTemplateModal interaction" duration="31"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers approval required display with many approvals" duration="13"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers different assessor values" duration="7"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers empty string values for task fields" duration="32"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers all remaining edge cases" duration="11"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers risk form type with template type fallback" duration="15"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers useEffect error handling simulation" duration="7"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers onClose function with editStep === 5" duration="12"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers button click handler for last updated by" duration="77"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers additional edge cases for comprehensive coverage" duration="29"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests achieves maximum possible coverage with current constraints" duration="190"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers getNamesByIds function with complex category structures" duration="12"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers getAtRisk function with is_other scenarios" duration="8"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers table column functions with edge cases" duration="14"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers date formatting with various date formats" duration="10"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers approval options with non-matching IDs" duration="10"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers empty parameter arrays in getAtRisk" duration="7"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers complex category and hazard structures" duration="27"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers parameter handling with is_other scenarios" duration="6"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers table rendering with many jobs" duration="8"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers date formatting with edge cases" duration="11"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers approval matching with mixed scenarios" duration="5"/>
        <testCase name="PreviewFormDetails Component Final Coverage Tests covers comprehensive edge case scenarios" duration="6"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/TemplateSelection/TemplateSelection.test.tsx">
        <testCase name="TemplateSelection renders main UI and handles template selection" duration="192"/>
        <testCase name="TemplateSelection selects a template from MostlyUsedCardList and enables Use Template button" duration="6"/>
        <testCase name="TemplateSelection disables Use Template button if no template is selected" duration="7"/>
        <testCase name="TemplateSelection calls navigation on Cancel button click" duration="7"/>
        <testCase name="TemplateSelection calls navigation on ExternalLinkIcon button click" duration="6"/>
        <testCase name="TemplateSelection calls navigation on Use Template click" duration="6"/>
        <testCase name="TemplateSelection fetches more templates when Fetch More is clicked" duration="2"/>
        <testCase name="TemplateSelection resets selectedTemplate when handleFilterChange is called" duration="5"/>
        <testCase name="TemplateSelection displays correct button text" duration="3"/>
        <testCase name="TemplateSelection renders template cards from CardGallery" duration="3"/>
        <testCase name="TemplateSelection renders All Templates section" duration="2"/>
        <testCase name="TemplateSelection renders external link icon in template preview button" duration="2"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/CreateRA/AddJobModal.test.tsx">
        <testCase name="AddJobModal Modal Rendering renders modal with correct title and structure" duration="112"/>
        <testCase name="AddJobModal Modal Rendering renders modal with correct size and backdrop properties" duration="56"/>
        <testCase name="AddJobModal Modal Rendering renders Cancel and Save Changes buttons" duration="28"/>
        <testCase name="AddJobModal Modal Rendering applies correct CSS classes to modal elements" duration="21"/>
        <testCase name="AddJobModal Props and Type Handling defaults to template type when type prop is not provided" duration="6"/>
        <testCase name="AddJobModal Props and Type Handling handles template type correctly" duration="6"/>
        <testCase name="AddJobModal Props and Type Handling handles risk type correctly" duration="11"/>
        <testCase name="AddJobModal Form Initialization initializes template form with empty template job" duration="10"/>
        <testCase name="AddJobModal Form Initialization initializes risk form with empty risk job" duration="11"/>
        <testCase name="AddJobModal Button States disables Save Changes button initially when form is empty" duration="13"/>
        <testCase name="AddJobModal Button States enables Save Changes button when form has changes and is valid" duration="43"/>
        <testCase name="AddJobModal Button States Cancel button is always enabled" duration="23"/>
        <testCase name="AddJobModal User Interactions calls onClose when Cancel button is clicked" duration="27"/>
        <testCase name="AddJobModal User Interactions calls onClose when modal is closed" duration="4"/>
        <testCase name="AddJobModal User Interactions handles form field changes correctly for template type" duration="11"/>
        <testCase name="AddJobModal User Interactions handles form field changes correctly for risk type" duration="82"/>
        <testCase name="AddJobModal Save Functionality calls save when validation passes for template type" duration="24"/>
        <testCase name="AddJobModal Save Functionality saves template job successfully when validation passes" duration="21"/>
        <testCase name="AddJobModal Save Functionality saves risk job successfully when validation passes" duration="27"/>
        <testCase name="AddJobModal Save Functionality generates UUID for template job when job_id is empty" duration="12"/>
        <testCase name="AddJobModal Save Functionality handles empty risk_job array when saving" duration="22"/>
        <testCase name="AddJobModal Save Functionality handles empty template_job array when saving" duration="13"/>
        <testCase name="AddJobModal Form State Management tracks changes correctly when multiple fields are filled" duration="44"/>
        <testCase name="AddJobModal Form State Management handles validation state changes correctly" duration="26"/>
        <testCase name="AddJobModal Form State Management preserves existing form data when adding new job" duration="20"/>
        <testCase name="AddJobModal Edge Cases and Error Handling handles form with minimal required properties" duration="3"/>
        <testCase name="AddJobModal Edge Cases and Error Handling handles null setForm function" duration="3"/>
        <testCase name="AddJobModal Edge Cases and Error Handling handles missing onClose function" duration="4"/>
        <testCase name="AddJobModal Edge Cases and Error Handling handles invalid type prop" duration="3"/>
        <testCase name="AddJobModal Edge Cases and Error Handling handles form with null job arrays" duration="2"/>
        <testCase name="AddJobModal Edge Cases and Error Handling handles form with undefined job arrays" duration="3"/>
        <testCase name="AddJobModal Integration with AddJobsStep passes correct props to AddJobsStep component" duration="8"/>
        <testCase name="AddJobModal Integration with AddJobsStep passes correct props to AddJobsStep for risk type" duration="9"/>
        <testCase name="AddJobModal Integration with AddJobsStep handles AddJobsStep validation callback correctly" duration="12"/>
        <testCase name="AddJobModal Integration with AddJobsStep handles AddJobsStep ref correctly" duration="21"/>
        <testCase name="AddJobModal Accessibility and UX has proper ARIA attributes" duration="9"/>
        <testCase name="AddJobModal Accessibility and UX focuses on modal when opened" duration="3"/>
        <testCase name="AddJobModal Accessibility and UX supports keyboard navigation" duration="19"/>
        <testCase name="AddJobModal Accessibility and UX displays proper button text and styling" duration="16"/>
        <testCase name="AddJobModal Performance and Memory cleans up properly when unmounted" duration="4"/>
        <testCase name="AddJobModal Performance and Memory handles rapid state changes without errors" duration="14"/>
        <testCase name="AddJobModal Performance and Memory handles modal rerendering correctly" duration="7"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/CreateRA/RiskRatingStep.test.tsx">
        <testCase name="RiskRatingStep Component Component Rendering renders the component with header by default" duration="79"/>
        <testCase name="RiskRatingStep Component Component Rendering renders without header when disableHeader is true" duration="25"/>
        <testCase name="RiskRatingStep Component Component Rendering renders worst case scenario input" duration="7"/>
        <testCase name="RiskRatingStep Component Component Rendering renders recovery measures input" duration="20"/>
        <testCase name="RiskRatingStep Component Component Rendering renders task reliability assessment section" duration="56"/>
        <testCase name="RiskRatingStep Component Component Rendering renders warning alert" duration="16"/>
        <testCase name="RiskRatingStep Component Risk Rating Calculation displays Medium risk rating when no assessments" duration="33"/>
        <testCase name="RiskRatingStep Component Risk Rating Calculation displays Medium risk rating when all answers are Yes" duration="13"/>
        <testCase name="RiskRatingStep Component Risk Rating Calculation displays High risk rating when all answers are No" duration="8"/>
        <testCase name="RiskRatingStep Component Risk Rating Calculation displays High risk rating when answers are mixed with No" duration="14"/>
        <testCase name="RiskRatingStep Component Form Field Updates updates worst case scenario field" duration="11"/>
        <testCase name="RiskRatingStep Component Form Field Updates updates recovery measures field" duration="9"/>
        <testCase name="RiskRatingStep Component Form Field Updates handles blur events for validation" duration="12"/>
        <testCase name="RiskRatingStep Component Form Field Updates displays form field values correctly" duration="15"/>
        <testCase name="RiskRatingStep Component Task Reliability Assessment renders radio options for each question" duration="10"/>
        <testCase name="RiskRatingStep Component Task Reliability Assessment shows selected answers correctly" duration="11"/>
        <testCase name="RiskRatingStep Component Task Reliability Assessment handles radio button changes" duration="13"/>
        <testCase name="RiskRatingStep Component Task Reliability Assessment shows condition input when Yes is selected" duration="8"/>
        <testCase name="RiskRatingStep Component Task Reliability Assessment handles condition input changes" duration="10"/>
        <testCase name="RiskRatingStep Component Task Reliability Assessment hides condition input when No is selected" duration="8"/>
        <testCase name="RiskRatingStep Component Validation validates required fields are empty" duration="5"/>
        <testCase name="RiskRatingStep Component Validation validates successfully when all fields are filled" duration="3"/>
        <testCase name="RiskRatingStep Component Validation validates task reliability assessment answers" duration="4"/>
        <testCase name="RiskRatingStep Component Edge Cases handles missing task reliability assessment list gracefully" duration="2"/>
        <testCase name="RiskRatingStep Component Edge Cases handles undefined task reliability assessment list" duration="2"/>
        <testCase name="RiskRatingStep Component Edge Cases handles form with empty task name" duration="4"/>
        <testCase name="RiskRatingStep Component Edge Cases handles assessment answer updates correctly" duration="6"/>
        <testCase name="RiskRatingStep Component Edge Cases handles new assessment creation when not exists" duration="32"/>
        <testCase name="RiskRatingStep Component Edge Cases handles condition updates for existing assessments" duration="6"/>
        <testCase name="RiskRatingStep Component Risk Rating Colors displays High risk rating correctly" duration="3"/>
        <testCase name="RiskRatingStep Component Risk Rating Colors displays High risk rating correctly for mixed answers" duration="3"/>
        <testCase name="RiskRatingStep Component Risk Rating Colors displays Medium risk rating correctly for all Yes answers" duration="13"/>
        <testCase name="RiskRatingStep Component Component Ref Methods exposes validate method through ref" duration="4"/>
        <testCase name="RiskRatingStep Component Component Ref Methods validate method returns boolean" duration="7"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/DeleteJobModal.test.tsx">
        <testCase name="DeleteJobModal renders modal with correct title and content" duration="64"/>
        <testCase name="DeleteJobModal renders Delete Job Step and Cancel buttons" duration="39"/>
        <testCase name="DeleteJobModal displays correct job information for the selected job" duration="13"/>
        <testCase name="DeleteJobModal displays correct job information for first job" duration="7"/>
        <testCase name="DeleteJobModal displays correct job information for last job" duration="7"/>
        <testCase name="DeleteJobModal calls onClose when Cancel button is clicked" duration="9"/>
        <testCase name="DeleteJobModal removes job from form and calls onClose when Delete Job Step is clicked" duration="13"/>
        <testCase name="DeleteJobModal handles empty template_job array" duration="7"/>
        <testCase name="DeleteJobModal handles non-existent jobId" duration="6"/>
        <testCase name="DeleteJobModal handles undefined template_job" duration="3"/>
        <testCase name="DeleteJobModal renders with correct CSS classes and structure" duration="9"/>
        <testCase name="DeleteJobModal renders modal with correct properties" duration="14"/>
        <testCase name="DeleteJobModal handles job deletion when jobId is empty string" duration="21"/>
        <testCase name="DeleteJobModal preserves other form properties when deleting job" duration="16"/>
        <testCase name="DeleteJobModal Risk Form Tests renders modal correctly for risk form" duration="7"/>
        <testCase name="DeleteJobModal Risk Form Tests displays correct job information for risk form" duration="3"/>
        <testCase name="DeleteJobModal Risk Form Tests displays correct job information for first risk job" duration="2"/>
        <testCase name="DeleteJobModal Risk Form Tests displays correct job information for last risk job" duration="5"/>
        <testCase name="DeleteJobModal Risk Form Tests removes job from risk form and calls onClose when Delete Job Step is clicked" duration="20"/>
        <testCase name="DeleteJobModal Risk Form Tests handles invalid jobId for risk form" duration="15"/>
        <testCase name="DeleteJobModal Risk Form Tests handles out of bounds jobId for risk form" duration="3"/>
        <testCase name="DeleteJobModal Risk Form Tests handles empty risk_job array" duration="10"/>
        <testCase name="DeleteJobModal Risk Form Tests handles undefined risk_job" duration="7"/>
        <testCase name="DeleteJobModal Risk Form Tests preserves other risk form properties when deleting job" duration="5"/>
        <testCase name="DeleteJobModal Risk Form Tests handles job deletion when jobId is negative for risk form" duration="13"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/CategoriesFiltersDrawer.test.tsx">
        <testCase name="CategoriesFiltersDrawer Basic Rendering renders trigger button with &quot;More Filters&quot; when no filters are selected" duration="20"/>
        <testCase name="CategoriesFiltersDrawer Basic Rendering renders trigger button with filter count when filters are selected" duration="7"/>
        <testCase name="CategoriesFiltersDrawer Basic Rendering renders drawer content with both filter sections" duration="2"/>
        <testCase name="CategoriesFiltersDrawer Basic Rendering renders Clear and Apply buttons" duration="3"/>
        <testCase name="CategoriesFiltersDrawer Filter Count Calculation shows correct count with only RA categories" duration="5"/>
        <testCase name="CategoriesFiltersDrawer Filter Count Calculation shows correct count with only hazard categories" duration="2"/>
        <testCase name="CategoriesFiltersDrawer Filter Count Calculation shows correct count with both filter types" duration="3"/>
        <testCase name="CategoriesFiltersDrawer Filter Count Calculation handles empty arrays correctly" duration="2"/>
        <testCase name="CategoriesFiltersDrawer Button Functionality calls onFilterChange with null values when Clear is clicked" duration="6"/>
        <testCase name="CategoriesFiltersDrawer Button Functionality calls onFilterChange with current state when Apply is clicked" duration="3"/>
        <testCase name="CategoriesFiltersDrawer Button Functionality resets internal state when component receives new props" duration="3"/>
        <testCase name="CategoriesFiltersDrawer Button Functionality resets internal state when drawer is closed via onClose" duration="8"/>
        <testCase name="CategoriesFiltersDrawer Risk Categories Filter renders all risk categories as checkboxes" duration="17"/>
        <testCase name="CategoriesFiltersDrawer Risk Categories Filter shows correct checked state for risk categories" duration="7"/>
        <testCase name="CategoriesFiltersDrawer Risk Categories Filter handles risk category checkbox changes" duration="8"/>
        <testCase name="CategoriesFiltersDrawer Risk Categories Filter handles unchecking risk category checkbox" duration="11"/>
        <testCase name="CategoriesFiltersDrawer Risk Categories Filter renders search input for risk categories" duration="5"/>
        <testCase name="CategoriesFiltersDrawer Hazard Categories Filter renders all hazard categories as checkboxes" duration="2"/>
        <testCase name="CategoriesFiltersDrawer Hazard Categories Filter shows correct checked state for hazard categories" duration="4"/>
        <testCase name="CategoriesFiltersDrawer Hazard Categories Filter handles hazard category checkbox changes" duration="3"/>
        <testCase name="CategoriesFiltersDrawer Hazard Categories Filter handles unchecking hazard category checkbox" duration="5"/>
        <testCase name="CategoriesFiltersDrawer Hazard Categories Filter renders search input for hazard categories" duration="6"/>
        <testCase name="CategoriesFiltersDrawer Search Functionality filters risk categories based on search input" duration="7"/>
        <testCase name="CategoriesFiltersDrawer Search Functionality filters hazard categories based on search input" duration="3"/>
        <testCase name="CategoriesFiltersDrawer Search Functionality shows &quot;No results found&quot; message when search yields no results for RA categories" duration="14"/>
        <testCase name="CategoriesFiltersDrawer Search Functionality shows &quot;No results found&quot; message when search yields no results for hazard categories" duration="4"/>
        <testCase name="CategoriesFiltersDrawer Edge Cases handles empty risk category list" duration="5"/>
        <testCase name="CategoriesFiltersDrawer Edge Cases handles empty hazard list" duration="10"/>
        <testCase name="CategoriesFiltersDrawer Edge Cases handles null filter values correctly" duration="2"/>
        <testCase name="CategoriesFiltersDrawer Edge Cases handles undefined filter values correctly" duration="1"/>
        <testCase name="CategoriesFiltersDrawer Edge Cases handles case-insensitive search" duration="17"/>
        <testCase name="CategoriesFiltersDrawer Edge Cases clears search when empty string is provided" duration="7"/>
        <testCase name="CategoriesFiltersDrawer Integration Tests maintains separate state for RA and hazard categories" duration="18"/>
        <testCase name="CategoriesFiltersDrawer Integration Tests resets both categories when clear is clicked" duration="30"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/InfiniteScrollTable.test.tsx">
        <testCase name="InfiniteScrollTable Component renders table with data and headers" duration="50"/>
        <testCase name="InfiniteScrollTable Component displays loading spinner when isLoading is true" duration="33"/>
        <testCase name="InfiniteScrollTable Component displays no results message when data is empty" duration="5"/>
        <testCase name="InfiniteScrollTable Component handles sorting when clicking column headers" duration="26"/>
        <testCase name="InfiniteScrollTable Component renders sticky actions column" duration="17"/>
        <testCase name="InfiniteScrollTable Component fetches more data when scrolled to bottom and more pages exist" duration="5"/>
        <testCase name="InfiniteScrollTable Component does not fetch more data when at the last page" duration="107"/>
        <testCase name="InfiniteScrollTable Component does not fetch more data when already fetching" duration="104"/>
        <testCase name="InfiniteScrollTable Component handles the case when all content fits in view and more pages exist" duration="3"/>
        <testCase name="InfiniteScrollTable Component uses correct CSS classes for column styling" duration="15"/>
        <testCase name="InfiniteScrollTable Component applies virtualization to rows" duration="232"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RAListing/components/RAMoreFiltersDrawer.test.tsx">
        <testCase name="RAMoreFiltersDrawer renders trigger button and drawer" duration="63"/>
        <testCase name="RAMoreFiltersDrawer renders all basic filters except search" duration="7"/>
        <testCase name="RAMoreFiltersDrawer shows Add More Filters dropdown and can add optional filters" duration="81"/>
        <testCase name="RAMoreFiltersDrawer renders Clear and Apply buttons" duration="4"/>
        <testCase name="RAMoreFiltersDrawer can remove optional filters and calls onFilterChange" duration="171"/>
        <testCase name="RAMoreFiltersDrawer calls handleClearFilters and handleUpdateFilters" duration="9"/>
        <testCase name="RAMoreFiltersDrawer toggles optional filters on and off" duration="52"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/routes/route.config.test.ts">
        <testCase name="route.config IRoute interface should define correct interface structure" duration="2"/>
        <testCase name="route.config IRoute interface should allow optional properties" duration="0"/>
        <testCase name="route.config routesConfig function Basic functionality should return an array of routes" duration="0"/>
        <testCase name="route.config routesConfig function Basic functionality should return routes with correct structure" duration="4"/>
        <testCase name="route.config routesConfig function Route definitions should define template creation route correctly" duration="0"/>
        <testCase name="route.config routesConfig function Route definitions should define template selection route with correct permission" duration="1"/>
        <testCase name="route.config routesConfig function Route definitions should define all StepperPage routes correctly" duration="0"/>
        <testCase name="route.config routesConfig function Permission handling should set isPermission to true when user has permission" duration="1"/>
        <testCase name="route.config routesConfig function Permission handling should set isPermission to false when user lacks permission" duration="10"/>
        <testCase name="route.config routesConfig function Permission handling should use roleConfig.riskAssessment.hasPermision for most routes" duration="2"/>
        <testCase name="route.config routesConfig function Route paths should have correct path structure" duration="0"/>
        <testCase name="route.config routesConfig function Route paths should not have leading slashes in paths" duration="1"/>
        <testCase name="route.config routesConfig function Route paths should have hierarchical path structure" duration="0"/>
        <testCase name="route.config routesConfig function Component assignments should have all components defined" duration="0"/>
        <testCase name="route.config routesConfig function Edge cases and error handling should handle null roleConfig gracefully" duration="30"/>
        <testCase name="route.config routesConfig function Edge cases and error handling should handle undefined roleConfig gracefully" duration="0"/>
        <testCase name="route.config routesConfig function Edge cases and error handling should handle roleConfig without riskAssessment property" duration="0"/>
        <testCase name="route.config routesConfig function Edge cases and error handling should handle roleConfig with null riskAssessment" duration="1"/>
        <testCase name="route.config routesConfig function Return value consistency should return consistent results for same input" duration="0"/>
        <testCase name="route.config routesConfig function Return value consistency should return different results for different permissions" duration="2"/>
        <testCase name="route.config routesConfig function Route configuration completeness should not have redirect properties" duration="0"/>
        <testCase name="route.config routesConfig function Route configuration completeness should not have childRoutes properties" duration="1"/>
        <testCase name="route.config routesConfig function Route configuration completeness should have all required properties for routing" duration="3"/>
        <testCase name="route.config Integration with routing system should be compatible with React Router structure" duration="2"/>
        <testCase name="route.config Integration with routing system should provide all necessary route information" duration="0"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/CreateRA/AddApproverCard.test.tsx">
        <testCase name="AddApproverCard renders Office Approval title" duration="62"/>
        <testCase name="AddApproverCard shows message when raLevel is not provided" duration="16"/>
        <testCase name="AddApproverCard renders existing approver for ROUTINE raLevel" duration="6"/>
        <testCase name="AddApproverCard renders multiple existing approvers (only first visible)" duration="3"/>
        <testCase name="AddApproverCard renders AsyncSearchCrewMember for CRITICAL raLevel and PUBLISHED status with no approvers" duration="3"/>
        <testCase name="AddApproverCard does not render AsyncSearchCrewMember if there are existing approvers" duration="3"/>
        <testCase name="AddApproverCard renders Pending in all card header tiles for ROUTINE raLevel regardless of approver status" duration="4"/>
        <testCase name="AddApproverCard renders AsyncSearchCrewMember for slot logic (approval_order)" duration="2"/>
        <testCase name="AddApproverCard shows Assign button and calls assignApproversToRA when all slots filled and no existing approvers" duration="0"/>
        <testCase name="AddApproverCard shows error toast if assignApproversToRA fails" duration="0"/>
        <testCase name="AddApproverCard renders ExistingApprover with message for approval and rejection" duration="5"/>
        <testCase name="AddApproverCard renders null for unknown raLevel" duration="6"/>
        <testCase name="AddApproverCard renders all reviewer slots with correct status and triggers reassign/approve/reject modals" duration="10"/>
        <testCase name="AddApproverCard - edge/branch/fallback coverage getApproverStatusText covers all branches" duration="4"/>
        <testCase name="AddApproverCard - edge/branch/fallback coverage getRaStatusText covers all branches" duration="0"/>
        <testCase name="AddApproverCard - edge/branch/fallback coverage ExistingApprover: fallback for null/undefined/invalid props" duration="1"/>
        <testCase name="covers fetchOfficeApprovers function with search functionality" duration="1"/>
        <testCase name="covers reAssignApprove function with success and error paths" duration="9"/>
        <testCase name="covers approveOrRejectRisk function" duration="2"/>
        <testCase name="covers ExistingApprover canPerformAction logic with different conditions" duration="2"/>
        <testCase name="covers ExistingApprover with assignedApproversSorted and index logic" duration="1"/>
        <testCase name="covers nextReviewTitle logic for different index values" duration="3"/>
        <testCase name="covers AsyncSearchCrewMember onChange functionality" duration="1"/>
        <testCase name="covers userCanBeReAssigned logic with different conditions" duration="18"/>
        <testCase name="covers fetchOfficeApprovers with filtering logic" duration="2"/>
        <testCase name="covers error handling in reAssignApprove function" duration="2"/>
        <testCase name="covers assign button functionality with error handling" duration="1"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/CreateRA/AddJobsStep.test.tsx">
        <testCase name="AddJobsStep Component Component Rendering renders the component with task title" duration="157"/>
        <testCase name="AddJobsStep Component Component Rendering renders guidance and risk matrix buttons" duration="93"/>
        <testCase name="AddJobsStep Component Component Rendering renders add job button" duration="91"/>
        <testCase name="AddJobsStep Component Component Rendering renders job cards when jobs exist" duration="39"/>
        <testCase name="AddJobsStep Component Component Rendering renders expand job cards link when multiple jobs exist" duration="99"/>
        <testCase name="AddJobsStep Component Component Rendering displays empty title when task_requiring_ra is empty" duration="72"/>
        <testCase name="AddJobsStep Component Component Rendering renders hazard and control measures section" duration="25"/>
        <testCase name="AddJobsStep Component Job Management expands job card when header is clicked" duration="43"/>
        <testCase name="AddJobsStep Component Job Management shows job step in collapsed header when available" duration="33"/>
        <testCase name="AddJobsStep Component Job Management toggles job card expansion correctly" duration="30"/>
        <testCase name="AddJobsStep Component Job Management deletes job when delete button is clicked" duration="57"/>
        <testCase name="AddJobsStep Component Job Management expands all job cards when expand job cards link is clicked" duration="174"/>
        <testCase name="AddJobsStep Component Form Field Updates updates job step field" duration="25"/>
        <testCase name="AddJobsStep Component Form Field Updates updates job hazard field" duration="22"/>
        <testCase name="AddJobsStep Component Form Field Updates updates nature of risk field" duration="13"/>
        <testCase name="AddJobsStep Component Form Field Updates updates existing control field" duration="31"/>
        <testCase name="AddJobsStep Component Form Field Updates updates additional mitigation field" duration="25"/>
        <testCase name="AddJobsStep Component Form Field Updates displays form field values correctly" duration="112"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality displays initial risk rating section" duration="21"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality displays residual risk rating section" duration="15"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality shows risk parameters for each rating type" duration="14"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality displays existing risk ratings" duration="12"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality opens risk rating modal when initial risk rating is clicked" duration="24"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality opens risk rating modal when residual risk rating is clicked" duration="15"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality handles risk rating selection from modal" duration="22"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality closes modal when close button is clicked" duration="16"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality updates reason for lowering when text is entered" duration="15"/>
        <testCase name="AddJobsStep Component Risk Rating Functionality displays existing reason for lowering" duration="42"/>
        <testCase name="AddJobsStep Component External Links opens guidance table PDF when button is clicked" duration="9"/>
        <testCase name="AddJobsStep Component External Links opens risk matrix PDF when button is clicked" duration="46"/>
        <testCase name="AddJobsStep Component Empty State renders correctly when no jobs exist" duration="16"/>
        <testCase name="AddJobsStep Component Keyboard Navigation handles keyboard navigation for expand job cards link" duration="156"/>
        <testCase name="AddJobsStep Component Edge Cases handles missing risk parameters gracefully" duration="65"/>
        <testCase name="AddJobsStep Component Edge Cases handles job without initial risk rating for residual section" duration="41"/>
        <testCase name="AddJobsStep Component Validation validates required fields correctly" duration="15"/>
        <testCase name="AddJobsStep Component Validation validates empty form correctly" duration="24"/>
        <testCase name="AddJobsStep Component Validation validates form with no jobs" duration="22"/>
        <testCase name="AddJobsStep Component Risk Rating Utilities handles risk rating modal with correct title" duration="20"/>
        <testCase name="AddJobsStep Component Risk Rating Utilities handles residual risk rating modal with correct title" duration="32"/>
        <testCase name="AddJobsStep Component Risk Rating Utilities displays correct IRR value in residual modal" duration="30"/>
        <testCase name="AddJobsStep Component Form Field Interactions handles onBlur events for form fields" duration="42"/>
        <testCase name="AddJobsStep Component Form Field Interactions handles maxLength constraints" duration="36"/>
        <testCase name="AddJobsStep Component Component Props works without onValidate callback" duration="36"/>
        <testCase name="AddJobsStep Component Risk Type Support renders correctly for risk type" duration="71"/>
        <testCase name="AddJobsStep Component Risk Type Support handles job changes for risk type" duration="99"/>
        <testCase name="AddJobsStep Component Risk Type Support handles job deletion for risk type" duration="70"/>
        <testCase name="AddJobsStep Component Risk Type Support handles adding new job for risk type" duration="66"/>
        <testCase name="AddJobsStep Component Risk Type Support validates risk form correctly" duration="37"/>
        <testCase name="AddJobsStep Component Risk Type Support shows close out section for risk type" duration="49"/>
        <testCase name="AddJobsStep Component Add Job Functionality adds first job when no jobs exist" duration="1"/>
        <testCase name="AddJobsStep Component Add Job Functionality validates before adding new job when jobs exist" duration="10"/>
        <testCase name="AddJobsStep Component Add Job Functionality adds new job when validation passes" duration="13"/>
        <testCase name="AddJobsStep Component Additional Mitigation Field handles onBlur for additional mitigation field" duration="8"/>
        <testCase name="AddJobsStep Component Reason Validation validates reason errors correctly" duration="177"/>
        <testCase name="AddJobsStep Component Form Update Scenarios handles form updates for risk type correctly" duration="153"/>
        <testCase name="AddJobsStep Component Form Update Scenarios handles form updates for template type correctly" duration="15"/>
        <testCase name="AddJobsStep Component Form Update Scenarios handles job deletion for risk type correctly" duration="52"/>
        <testCase name="AddJobsStep Component Form Update Scenarios handles additional mitigation onBlur correctly" duration="35"/>
        <testCase name="AddJobsStep Component Form Update Scenarios handles template job deletion correctly" duration="16"/>
        <testCase name="AddJobsStep Component Form Update Scenarios handles adding new job for risk type when no jobs exist" duration="1"/>
        <testCase name="AddJobsStep Component Form Update Scenarios handles adding new job for template type when no jobs exist" duration="5"/>
        <testCase name="AddJobsStep Component Form Update Scenarios handles createNewJob for risk type when no jobs exist" duration="3"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests handles close out date change for risk type" duration="47"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests handles close out responsibility change for risk type" duration="40"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests handles risk rating button disabled state correctly" duration="17"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests handles modal selection for initial risk rating" duration="18"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests handles modal selection for residual risk rating" duration="31"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests handles isEdit mode correctly" duration="9"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests handles empty risk parameters list" duration="0"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests handles stop propagation on delete button click" duration="0"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests handles disabled residual risk rating button click" duration="16"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests handles validation with reason errors" duration="8"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests covers edge cases in risk rating display" duration="10"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests handles additional mitigation onBlur with setTouched" duration="22"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests handles risk type job change with validateAndNotify" duration="50"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests handles template type job change with validateAndNotify" duration="38"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests handles risk rating selection when no existing rating exists" duration="27"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests handles modal selection with null selectedJobIdx" duration="7"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests handles risk rating update for existing rating" duration="19"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests handles error scenarios in component lifecycle" duration="15"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers onBlur for job_nature_of_risk field (line 475)" duration="15"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers onBlur for job_existing_control field (line 500)" duration="10"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers close out responsibility onChange (line 567)" duration="77"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers risk type field name assignment (line 647)" duration="60"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers early return when selectedJobIdx or selectedRiskCategory is null (line 710)" duration="54"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers early return when job is not found (line 804)" duration="3"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers risk type rating array assignment (line 809)" duration="111"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers new rating creation when existing index is -1 (line 861)" duration="26"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers risk type job deletion (lines 1098-1102)" duration="88"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers additional mitigation onBlur with setTouched (lines 1283-1286)" duration="22"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers handleModalSelection with null selectedJobIdx" duration="14"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers getExistingRating with invalid job index" duration="9"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers updateReasonForLowering with new rating creation" duration="48"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers template type job deletion path" duration="21"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers residual risk rating modal for risk type" duration="36"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers setTouchedField function (line 413)" duration="12"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers job_step onBlur (line 430)" duration="20"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers job_hazard onBlur (line 454)" duration="14"/>
        <testCase name="AddJobsStep Component Additional Coverage Tests Branch Coverage Tests covers all remaining uncovered branches" duration="15"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/CreateRA/AddTeamMembersStep.test.tsx">
        <testCase name="AddTeamMembersStep renders empty state when no team members are present" duration="46"/>
        <testCase name="AddTeamMembersStep fetches crew list on mount when vessel_id is present" duration="10"/>
        <testCase name="AddTeamMembersStep handles error when fetching crew list fails" duration="5"/>
        <testCase name="AddTeamMembersStep does not fetch crew list when vessel_id is not present" duration="33"/>
        <testCase name="AddTeamMembersStep displays task header with correct information" duration="17"/>
        <testCase name="AddTeamMembersStep displays team member when present" duration="9"/>
        <testCase name="AddTeamMembersStep calls onValidate with true when team members are present" duration="8"/>
        <testCase name="AddTeamMembersStep calls onValidate with false when no team members are present" duration="8"/>
        <testCase name="AddTeamMembersStep handles empty risk_team_member array" duration="11"/>
        <testCase name="AddTeamMembersStep does not add team member if selectedIds is empty" duration="9"/>
        <testCase name="AddTeamMembersStep does not add team member if crew member is not found" duration="18"/>
        <testCase name="AddTeamMembersStep does not add duplicate team member" duration="6"/>
        <testCase name="AddTeamMembersStep handles adding team member with string ID" duration="17"/>
        <testCase name="AddTeamMembersStep renders in edit mode without header" duration="12"/>
        <testCase name="AddTeamMembersStep uses crew members from context if available" duration="13"/>
        <testCase name="AddTeamMembersStep validates through ref" duration="21"/>
        <testCase name="AddTeamMembersStep handles team member selection with various ID formats" duration="7"/>
        <testCase name="AddTeamMembersStep Office Member Functionality handles office member search functionality" duration="167"/>
        <testCase name="AddTeamMembersStep Office Member Functionality removes office member from team" duration="116"/>
        <testCase name="AddTeamMembersStep Office Member Functionality does not search when query is too short" duration="120"/>
        <testCase name="AddTeamMembersStep Office Member Functionality handles empty search query" duration="108"/>
        <testCase name="AddTeamMembersStep Office Member Functionality adds office member to team successfully" duration="7"/>
        <testCase name="AddTeamMembersStep Office Member Functionality does not add duplicate office member" duration="12"/>
        <testCase name="AddTeamMembersStep Office Member Functionality handles office member without last name" duration="115"/>
        <testCase name="AddTeamMembersStep Additional Coverage Tests displays LevelOfRATag when ra_level is 4" duration="7"/>
        <testCase name="AddTeamMembersStep Additional Coverage Tests does not display LevelOfRATag when ra_level is not 4" duration="4"/>
        <testCase name="AddTeamMembersStep Additional Coverage Tests handles form without date_risk_assessment" duration="5"/>
        <testCase name="AddTeamMembersStep Additional Coverage Tests handles form without task_requiring_ra" duration="4"/>
        <testCase name="AddTeamMembersStep Additional Coverage Tests handles crew member with different styling in edit mode" duration="7"/>
        <testCase name="AddTeamMembersStep Additional Coverage Tests handles office member display correctly" duration="11"/>
        <testCase name="AddTeamMembersStep Additional Coverage Tests handles office member without rank" duration="8"/>
        <testCase name="AddTeamMembersStep Additional Coverage Tests handles office member without email" duration="12"/>
        <testCase name="AddTeamMembersStep Additional Coverage Tests validates form without risk_team_member property" duration="4"/>
        <testCase name="AddTeamMembersStep Additional Coverage Tests handles form without vessel_id property" duration="8"/>
        <testCase name="AddTeamMembersStep Additional Coverage Tests covers addTeamMember function with crew member addition" duration="13"/>
        <testCase name="AddTeamMembersStep Additional Coverage Tests covers removeTeamMember function with office member removal" duration="15"/>
        <testCase name="AddTeamMembersStep Additional Coverage Tests covers removeTeamMember function with mixed member types" duration="8"/>
        <testCase name="AddTeamMembersStep Additional Coverage Tests covers fetchOfficeApprovers function edge cases" duration="113"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/SearchDropdown.test.tsx">
        <testCase name="SearchDropdown renders placeholder when nothing selected" duration="25"/>
        <testCase name="SearchDropdown renders selected values" duration="9"/>
        <testCase name="SearchDropdown opens dropdown and filters options" duration="26"/>
        <testCase name="SearchDropdown calls onChange when option is selected" duration="7"/>
        <testCase name="SearchDropdown shows select all/clear all button" duration="4"/>
        <testCase name="SearchDropdown shows &quot;No options found.&quot; when search yields no results" duration="6"/>
        <testCase name="SearchDropdown calls onChange for select all" duration="3"/>
        <testCase name="SearchDropdown calls onChange for clear all" duration="5"/>
        <testCase name="SearchDropdown handles empty options array" duration="5"/>
        <testCase name="SearchDropdown closes dropdown on outside click" duration="6"/>
        <testCase name="SearchDropdown keyboard accessibility: Enter, Space, Escape" duration="12"/>
        <testCase name="SearchDropdown has proper aria attributes" duration="1"/>
        <testCase name="SearchDropdown shows counter and SingleBadgePopover when more than maxDisplayNames selected" duration="3"/>
        <testCase name="SearchDropdown does not crash if refs are not set in useLayoutEffect" duration="1"/>
        <testCase name="SearchDropdown does not call onChange for select all/clear all when no options" duration="12"/>
        <testCase name="SearchDropdown calls onChange when CheckboxComponent onChange is triggered" duration="4"/>
        <testCase name="SearchDropdown does not close dropdown on outside click if dropdownRef is not set" duration="2"/>
        <testCase name="SearchDropdown handles setMaxDisplayNames when namesMeasureRef has no children" duration="2"/>
        <testCase name="SearchDropdown works in single select mode" duration="2"/>
        <testCase name="SearchDropdown shows clear icon in single select mode when item is selected" duration="1"/>
        <testCase name="SearchDropdown shows chevron in single select mode when no item is selected" duration="1"/>
        <testCase name="SearchDropdown clears selection when clear icon is clicked in single select mode" duration="1"/>
        <testCase name="SearchDropdown clears selection when clear icon is activated with keyboard in single select mode" duration="1"/>
        <testCase name="SearchDropdown clears selection when clear icon is activated with spacebar in single select mode" duration="1"/>
        <testCase name="SearchDropdown prevents event propagation when clear icon is clicked" duration="1"/>
        <testCase name="SearchDropdown handles keyboard navigation on dropdown options" duration="5"/>
        <testCase name="SearchDropdown handles spacebar on dropdown options" duration="3"/>
        <testCase name="SearchDropdown ignores other keys on dropdown options" duration="9"/>
        <testCase name="SearchDropdown deselects option when already selected in multiple mode" duration="3"/>
        <testCase name="SearchDropdown selects all filtered options when some are already selected" duration="4"/>
        <testCase name="SearchDropdown clears all filtered options when all are selected" duration="3"/>
        <testCase name="SearchDropdown handles null selected prop" duration="1"/>
        <testCase name="SearchDropdown handles window resize" duration="3"/>
        <testCase name="SearchDropdown triggers useLayoutEffect when dropdown opens with selected options" duration="5"/>
        <testCase name="SearchDropdown sets aria-selected correctly on options" duration="6"/>
        <testCase name="SearchDropdown applies correct className for single select options" duration="12"/>
        <testCase name="SearchDropdown does not render checkboxes in single select mode" duration="3"/>
        <testCase name="SearchDropdown does not render select all button in single select mode" duration="3"/>
        <testCase name="SearchDropdown handles null search value" duration="16"/>
        <testCase name="SearchDropdown adds option when not already selected in multiple mode" duration="20"/>
        <testCase name="SearchDropdown calculates display names based on available width" duration="31"/>
        <testCase name="SearchDropdown handles window resize when inputRef is available" duration="179"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/Popover.test.tsx">
        <testCase name="Popover renders the trigger element" duration="78"/>
        <testCase name="Popover shows popover on hover" duration="250"/>
        <testCase name="Popover shows popover on focus" duration="237"/>
        <testCase name="Popover hides popover on mouse leave" duration="338"/>
        <testCase name="Popover renders with correct popover classes" duration="221"/>
        <testCase name="Popover renders with different text content" duration="247"/>
        <testCase name="Popover works with different child elements" duration="256"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RAAproval/RAAproval.test.tsx">
        <testCase name="RAAproval renders loading state" duration="26"/>
        <testCase name="RAAproval renders error state" duration="4"/>
        <testCase name="RAAproval renders no data state" duration="4"/>
        <testCase name="RAAproval handles Cancel button" duration="7"/>
        <testCase name="RAAproval handles template fetch error" duration="4"/>
        <testCase name="RAAproval additional coverage shows Level of RA dropdown and handles Save for non-ROUTINE" duration="8"/>
        <testCase name="RAAproval additional coverage handles categories/hazards not matching template" duration="3"/>
        <testCase name="RAAproval additional coverage handles ROUTINE level with modal confirm" duration="6"/>
        <testCase name="RAAproval comprehensive coverage handles generatePDF success case" duration="19"/>
        <testCase name="RAAproval comprehensive coverage handles dropdown onChange with empty value" duration="19"/>
        <testCase name="RAAproval comprehensive coverage handles Cancel button in preview mode" duration="6"/>
        <testCase name="RAAproval comprehensive coverage handles beforeunload event listener setup and cleanup" duration="7"/>
        <testCase name="RAAproval comprehensive coverage handles loadBasicDetails with different data scenarios" duration="6"/>
        <testCase name="RAAproval comprehensive coverage handles loadBasicDetails with template data mismatch" duration="5"/>
        <testCase name="RAAproval comprehensive coverage handles setRaLevel with ROUTINE level and action date" duration="4"/>
        <testCase name="RAAproval comprehensive coverage handles setRaLevel with missing parameters" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles ROUTINE level selection and save button state" duration="7"/>
        <testCase name="RAAproval comprehensive coverage handles setRaLevel with invalid parameters (no levelOfRA)" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles setRaLevel with ROUTINE but no actionDate" duration="3"/>
        <testCase name="RAAproval comprehensive coverage handles hasFormChanges calculation with text field changes" duration="5"/>
        <testCase name="RAAproval comprehensive coverage handles hasFormChanges calculation with assessment changes" duration="4"/>
        <testCase name="RAAproval comprehensive coverage handles previewOnly calculation with different user scenarios" duration="6"/>
        <testCase name="RAAproval comprehensive coverage handles window beforeunload event properly" duration="2"/>
        <testCase name="RAAproval comprehensive coverage handles different approval status scenarios" duration="5"/>
        <testCase name="RAAproval comprehensive coverage handles edge case with empty risk_approver array" duration="45"/>
        <testCase name="RAAproval comprehensive coverage handles risk parameter data processing with complex data" duration="7"/>
        <testCase name="RAAproval comprehensive coverage handles loadBasicDetails with null/undefined parameter types" duration="6"/>
        <testCase name="RAAproval comprehensive coverage handles setDataStore call in loadBasicDetails" duration="8"/>
        <testCase name="RAAproval comprehensive coverage covers parseDate undefined branch in setRaLevel" duration="14"/>
        <testCase name="RAAproval comprehensive coverage covers handleBreadcrumbClick function" duration="17"/>
        <testCase name="RAAproval comprehensive coverage covers $content useMemo with different states" duration="51"/>
        <testCase name="RAAproval comprehensive coverage covers setForm in setRaLevel function" duration="44"/>
        <testCase name="RAAproval comprehensive coverage covers additional branches in component" duration="47"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RATemplateListing/components/ActionDropdownMenu.test.tsx">
        <testCase name="ActionDropdownMenu Component Rendering should render the dropdown menu with three dots icon" duration="20"/>
        <testCase name="ActionDropdownMenu Component Rendering should render with correct CSS classes for horizontal alignment by default" duration="3"/>
        <testCase name="ActionDropdownMenu Component Rendering should render with vertical alignment class when menuAlign is vertical" duration="1"/>
        <testCase name="ActionDropdownMenu Component Rendering should render dropdown structure with correct classes" duration="1"/>
        <testCase name="ActionDropdownMenu Menu Items should render dropdown with proper structure" duration="4"/>
        <testCase name="ActionDropdownMenu Menu Items should render component correctly when user has permission" duration="3"/>
        <testCase name="ActionDropdownMenu Menu Items should not render Archive modal when user does not have permission" duration="1"/>
        <testCase name="ActionDropdownMenu Menu Items should contain dropdown menu structure for menu items" duration="0"/>
        <testCase name="ActionDropdownMenu Menu Items should render dropdown component structure" duration="2"/>
        <testCase name="ActionDropdownMenu Menu Items should render dropdown toggle" duration="2"/>
        <testCase name="ActionDropdownMenu Menu Items should render three dots icon" duration="1"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should render component structure correctly regardless of modal visibility" duration="6"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should render component when user has permission" duration="1"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should render component correctly with different permission states" duration="1"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should render component with onSuccess callback when user has permission" duration="1"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should handle different data configurations without errors" duration="1"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should handle data with both created_at and createdAt fields" duration="0"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should handle data with only createdAt field" duration="2"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should handle different user details configurations" duration="1"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should handle empty user details gracefully" duration="0"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should handle onSuccess callback prop correctly" duration="1"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should render without onSuccess callback" duration="1"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should render component with correct data when user has permission" duration="3"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should handle user details with email only" duration="1"/>
        <testCase name="ActionDropdownMenu ArchiveTemplateModal Integration should handle empty user details" duration="1"/>
        <testCase name="ActionDropdownMenu Props Handling should handle menuAlign prop correctly" duration="0"/>
        <testCase name="ActionDropdownMenu Props Handling should default to horizontal alignment when menuAlign is not provided" duration="1"/>
        <testCase name="ActionDropdownMenu Component Structure and Rendering should not show PreviewTemplateModal initially" duration="1"/>
        <testCase name="ActionDropdownMenu Component Structure and Rendering should show PreviewTemplateModal when View Template is clicked" duration="45"/>
        <testCase name="ActionDropdownMenu Component Structure and Rendering should close PreviewTemplateModal when close button is clicked" duration="96"/>
        <testCase name="ActionDropdownMenu Component Structure and Rendering should render dropdown menu correctly" duration="1"/>
        <testCase name="ActionDropdownMenu Component Structure and Rendering should render component structure correctly" duration="1"/>
        <testCase name="ActionDropdownMenu Component Structure and Rendering should render component without errors" duration="1"/>
        <testCase name="ActionDropdownMenu Component Structure and Rendering should render component structure without errors" duration="1"/>
        <testCase name="ActionDropdownMenu Component Structure and Rendering should render component when user has archive permission" duration="1"/>
        <testCase name="ActionDropdownMenu Component Structure and Rendering should render component when user lacks archive permission" duration="0"/>
        <testCase name="ActionDropdownMenu Edge Cases should handle missing template data gracefully" duration="3"/>
        <testCase name="ActionDropdownMenu Edge Cases should handle null/undefined roleConfig gracefully" duration="1"/>
        <testCase name="ActionDropdownMenu Edge Cases should handle missing context gracefully" duration="328"/>
        <testCase name="ActionDropdownMenu Accessibility should have proper ARIA attributes for dropdown" duration="9"/>
        <testCase name="ActionDropdownMenu Accessibility should have focusable dropdown toggle" duration="2"/>
        <testCase name="ActionDropdownMenu Accessibility should have proper dropdown structure for screen readers" duration="1"/>
        <testCase name="ActionDropdownMenu Accessibility should have proper CSS classes for accessibility" duration="4"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/AsyncSearchCrewMember.test.tsx">
        <testCase name="AsyncSearchCrewMember renders with default placeholder and fetches data" duration="329"/>
        <testCase name="AsyncSearchCrewMember calls onChange with selected and originalData" duration="73"/>
        <testCase name="AsyncSearchCrewMember shows loading state when isLoading is true" duration="15"/>
        <testCase name="AsyncSearchCrewMember shows clear button and calls onChange([]) if value is empty and clear is clicked" duration="17"/>
        <testCase name="AsyncSearchCrewMember shows async message if search is less than 3 chars" duration="30"/>
        <testCase name="AsyncSearchCrewMember renders with a custom placeholder" duration="9"/>
        <testCase name="AsyncSearchCrewMember does not call onChange on blur if nothing is selected" duration="14"/>
        <testCase name="AsyncSearchCrewMember calls onChange([]) when clear is clicked even if already empty" duration="19"/>
        <testCase name="AsyncSearchCrewMember shows &quot;No users found.&quot; when fetch returns empty" duration="18"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/CreateRA/RAApprovalModal.test.tsx">
        <testCase name="RAApprovalModal opens modal on trigger click and closes on cancel" duration="365"/>
        <testCase name="RAApprovalModal shows approveWithComment UI and disables Approve until date and comment are filled" duration="61"/>
        <testCase name="RAApprovalModal resets state on close" duration="38"/>
        <testCase name="RAApprovalModal clones trigger and preserves other props" duration="5"/>
        <testCase name="RAApprovalModal does not break if trigger is null" duration="5"/>
        <testCase name="RAApprovalModal shows reject UI and handles rejection flow" duration="25"/>
        <testCase name="RAApprovalModal handles approve operation with date selection" duration="8"/>
        <testCase name="RAApprovalModal shows final approval warning for reviewIndex 2 with approve operation" duration="6"/>
        <testCase name="RAApprovalModal shows final approval with condition warning for reviewIndex 2 with approveWithComment operation" duration="14"/>
        <testCase name="RAApprovalModal shows success toast for approve operation" duration="22"/>
        <testCase name="RAApprovalModal shows success toast for reject operation" duration="38"/>
        <testCase name="RAApprovalModal shows success toast for approveWithComment operation" duration="13"/>
        <testCase name="RAApprovalModal handles error in toast and shows error toast" duration="21"/>
        <testCase name="RAApprovalModal disables buttons correctly for reject operation without date and comment" duration="9"/>
        <testCase name="RAApprovalModal disables buttons correctly for approveWithComment operation" duration="7"/>
        <testCase name="RAApprovalModal handles comment with only whitespace for approveWithComment" duration="6"/>
        <testCase name="RAApprovalModal handles comment with only whitespace for reject" duration="8"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/CreateRA/SubmitRoutineRAModal.test.tsx">
        <testCase name="SubmitRoutineRAModal Component Rendering renders trigger element correctly" duration="159"/>
        <testCase name="SubmitRoutineRAModal Component Rendering does not show modal initially" duration="32"/>
        <testCase name="SubmitRoutineRAModal Component Rendering shows modal when trigger is clicked" duration="144"/>
        <testCase name="SubmitRoutineRAModal Component Rendering renders modal content correctly when open" duration="19"/>
        <testCase name="SubmitRoutineRAModal Modal Interactions closes modal when Cancel button is clicked" duration="44"/>
        <testCase name="SubmitRoutineRAModal Modal Interactions resets approval date when modal is closed" duration="19"/>
        <testCase name="SubmitRoutineRAModal Modal Interactions disables Set &amp; Approve button when no approval date is selected" duration="12"/>
        <testCase name="SubmitRoutineRAModal Modal Interactions enables Set &amp; Approve button when approval date is selected" duration="9"/>
        <testCase name="SubmitRoutineRAModal Date Selection updates approval date when date is selected" duration="12"/>
        <testCase name="SubmitRoutineRAModal Date Selection clears approval date when date is cleared" duration="16"/>
        <testCase name="SubmitRoutineRAModal Form Submission calls onConfirm with correct parameters when form is submitted" duration="39"/>
        <testCase name="SubmitRoutineRAModal Form Submission shows success toast with custom message when onConfirm returns message" duration="16"/>
        <testCase name="SubmitRoutineRAModal Form Submission shows default success toast when onConfirm returns no message" duration="34"/>
        <testCase name="SubmitRoutineRAModal Form Submission shows default success toast when onConfirm returns undefined" duration="12"/>
        <testCase name="SubmitRoutineRAModal Form Submission closes modal after successful submission" duration="31"/>
        <testCase name="SubmitRoutineRAModal Error Handling shows error toast when onConfirm throws an error" duration="18"/>
        <testCase name="SubmitRoutineRAModal Error Handling closes modal after error" duration="22"/>
        <testCase name="SubmitRoutineRAModal Loading States disables buttons during submission" duration="17"/>
        <testCase name="SubmitRoutineRAModal CustomDatePicker Props passes correct props to CustomDatePicker" duration="9"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/ActionsDropdownCell.test.tsx">
        <testCase name="ActionsDropdownCell Component Rendering should render the dropdown component" duration="25"/>
        <testCase name="ActionsDropdownCell Component Rendering should render the three dots icon" duration="4"/>
        <testCase name="ActionsDropdownCell Component Rendering should render dropdown toggle with correct classes" duration="3"/>
        <testCase name="ActionsDropdownCell Component Rendering should render dropdown structure" duration="1"/>
        <testCase name="ActionsDropdownCell Dropdown Menu Items should render Edit and Delete menu items when dropdown is opened" duration="30"/>
        <testCase name="ActionsDropdownCell Dropdown Menu Items should render component without errors" duration="1"/>
        <testCase name="ActionsDropdownCell Edit Action Handler should call all required functions when Edit is clicked" duration="13"/>
        <testCase name="ActionsDropdownCell Edit Action Handler should call functions with correct parameters for different jobId" duration="10"/>
        <testCase name="ActionsDropdownCell Delete Action Handler should call required functions when Delete is clicked" duration="14"/>
        <testCase name="ActionsDropdownCell Delete Action Handler should call functions with correct jobId for different job" duration="16"/>
        <testCase name="ActionsDropdownCell Props Validation should handle empty jobId" duration="7"/>
        <testCase name="ActionsDropdownCell Props Validation should render without crashing when all props are provided" duration="0"/>
        <testCase name="ActionsDropdownCell Component Structure should have correct dropdown structure" duration="1"/>
        <testCase name="ActionsDropdownCell Component Structure should have proper CSS classes applied" duration="1"/>
        <testCase name="ActionsDropdownCell Component Structure should have dropdown menu with correct classes when opened" duration="13"/>
        <testCase name="ActionsDropdownCell Accessibility should have proper dropdown structure for screen readers" duration="2"/>
        <testCase name="ActionsDropdownCell Accessibility should render menu items with proper text content when opened" duration="14"/>
        <testCase name="ActionsDropdownCell Dropdown Interaction should toggle dropdown when clicked" duration="15"/>
        <testCase name="ActionsDropdownCell Dropdown Interaction should have proper popper configuration" duration="1"/>
        <testCase name="ActionsDropdownCell Edge Cases and Error Handling should handle null or undefined jobId gracefully" duration="1"/>
        <testCase name="ActionsDropdownCell Edge Cases and Error Handling should handle special characters in jobId" duration="9"/>
        <testCase name="ActionsDropdownCell Edge Cases and Error Handling should maintain component state after multiple interactions" duration="25"/>
        <testCase name="ActionsDropdownCell Performance and Optimization should not re-render unnecessarily when props do not change" duration="1"/>
        <testCase name="ActionsDropdownCell Performance and Optimization should handle rapid successive clicks gracefully" duration="14"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/GroupedCheckboxGrid.test.tsx">
        <testCase name="GroupedCheckboxGrid renders title, subtitle and group labels" duration="80"/>
        <testCase name="GroupedCheckboxGrid calls onChange when a checkbox is selected" duration="21"/>
        <testCase name="GroupedCheckboxGrid toggles &quot;Others&quot; checkbox and inputs text" duration="47"/>
        <testCase name="GroupedCheckboxGrid trims &quot;Others&quot; input to max length" duration="132"/>
        <testCase name="GroupedCheckboxGrid removes &quot;Others&quot; text when unchecked" duration="48"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RATemplateListing/components/MostlyUsedCard.test.tsx">
        <testCase name="MostlyUsedCard Component Rendering renders the component with all required elements" duration="38"/>
        <testCase name="MostlyUsedCard Component Rendering has the correct CSS class structure" duration="8"/>
        <testCase name="MostlyUsedCard Component Rendering renders menu dots correctly" duration="3"/>
        <testCase name="MostlyUsedCard Template Name Display displays template name through TruncateText component" duration="4"/>
        <testCase name="MostlyUsedCard Template Name Display passes correct maxLength to TruncateText component" duration="5"/>
        <testCase name="MostlyUsedCard Template Name Display handles empty template name" duration="1"/>
        <testCase name="MostlyUsedCard Categories Display displays risk categories count correctly" duration="3"/>
        <testCase name="MostlyUsedCard Categories Display displays hazard categories count correctly" duration="4"/>
        <testCase name="MostlyUsedCard Categories Display handles zero categories" duration="9"/>
        <testCase name="MostlyUsedCard Categories Display handles large category numbers" duration="9"/>
        <testCase name="MostlyUsedCard Keywords Display displays keywords count in label" duration="6"/>
        <testCase name="MostlyUsedCard Keywords Display passes keywords to SingleBadgePopover component" duration="5"/>
        <testCase name="MostlyUsedCard Keywords Display handles empty keywords array" duration="3"/>
        <testCase name="MostlyUsedCard Keywords Display handles single keyword" duration="3"/>
        <testCase name="MostlyUsedCard Keywords Display handles keywords with special characters" duration="3"/>
        <testCase name="MostlyUsedCard Date Display displays formatted creation date" duration="4"/>
        <testCase name="MostlyUsedCard Date Display handles different date formats" duration="4"/>
        <testCase name="MostlyUsedCard Date Display handles invalid date gracefully" duration="23"/>
        <testCase name="MostlyUsedCard Date Display handles empty date string" duration="3"/>
        <testCase name="MostlyUsedCard User Avatar displays initials of username in avatar" duration="1"/>
        <testCase name="MostlyUsedCard User Avatar handles single character username" duration="3"/>
        <testCase name="MostlyUsedCard User Avatar handles lowercase username" duration="118"/>
        <testCase name="MostlyUsedCard User Avatar handles username with special characters" duration="11"/>
        <testCase name="MostlyUsedCard User Avatar handles empty username gracefully" duration="11"/>
        <testCase name="MostlyUsedCard Props Variations handles all minimum values" duration="4"/>
        <testCase name="MostlyUsedCard Props Variations handles all maximum realistic values" duration="4"/>
        <testCase name="MostlyUsedCard Component Structure and Accessibility maintains proper DOM structure" duration="1"/>
        <testCase name="MostlyUsedCard Component Structure and Accessibility has proper avatar structure with menu dots" duration="1"/>
        <testCase name="MostlyUsedCard Integration with Dependencies calls parseDate utility with correct parameters" duration="5"/>
        <testCase name="MostlyUsedCard Integration with Dependencies passes correct props to TruncateText component" duration="6"/>
        <testCase name="MostlyUsedCard Integration with Dependencies passes correct props to SingleBadgePopover component" duration="4"/>
        <testCase name="MostlyUsedCard Optional Props applies custom className when provided" duration="1"/>
        <testCase name="MostlyUsedCard Optional Props passes menuAlign prop to ActionDropdownMenu" duration="3"/>
        <testCase name="MostlyUsedCard Optional Props passes menuAlign horizontal to ActionDropdownMenu" duration="4"/>
        <testCase name="MostlyUsedCard Optional Props hides menu when hideMenu is true" duration="4"/>
        <testCase name="MostlyUsedCard Optional Props shows menu when hideMenu is false" duration="3"/>
        <testCase name="MostlyUsedCard Optional Props shows menu by default when hideMenu is not provided" duration="2"/>
        <testCase name="MostlyUsedCard ActionDropdownMenu Data Structure passes correct data structure to ActionDropdownMenu" duration="3"/>
        <testCase name="MostlyUsedCard ActionDropdownMenu Data Structure passes userDetails with correct structure to ActionDropdownMenu" duration="4"/>
        <testCase name="MostlyUsedCardList Loading State displays loading state when data is being fetched" duration="9"/>
        <testCase name="MostlyUsedCardList Empty State returns null when no data is available" duration="0"/>
        <testCase name="MostlyUsedCardList Empty State returns null when results array is empty" duration="1"/>
        <testCase name="MostlyUsedCardList Success State renders title and cards container" duration="11"/>
        <testCase name="MostlyUsedCardList Success State renders MostlyUsedCard components for each template" duration="4"/>
        <testCase name="MostlyUsedCardList Success State limits display to maximum 4 templates" duration="11"/>
        <testCase name="MostlyUsedCardList Success State passes correct props to MostlyUsedCard components" duration="8"/>
        <testCase name="MostlyUsedCardList Success State handles user details mapping correctly" duration="5"/>
        <testCase name="MostlyUsedCardList Success State falls back to created_by when user details not found" duration="5"/>
        <testCase name="MostlyUsedCardList Success State passes menuAlign as vertical to cards" duration="14"/>
        <testCase name="MostlyUsedCardList Error Handling calls toast.error when onError is triggered" duration="1"/>
        <testCase name="MostlyUsedCardList Error Handling calls toast.error with default message when error has no message" duration="0"/>
        <testCase name="MostlyUsedCardList Hook Integration calls useQuery with correct parameters" duration="5"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/ConfirmPublishDetailsModal.test.tsx">
        <testCase name="ConfirmPublishDetailsModal renders modal with initial keywords" duration="28"/>
        <testCase name="ConfirmPublishDetailsModal adds a new keyword on Enter key press" duration="77"/>
        <testCase name="ConfirmPublishDetailsModal does not add duplicate keywords" duration="44"/>
        <testCase name="ConfirmPublishDetailsModal removes keyword when X icon is clicked" duration="22"/>
        <testCase name="ConfirmPublishDetailsModal calls onSave with keywords and closes on confirm" duration="59"/>
        <testCase name="ConfirmPublishDetailsModal calls onClose without saving on cancel" duration="14"/>
        <testCase name="ConfirmPublishDetailsModal disables confirm button when no keywords" duration="66"/>
        <testCase name="ConfirmPublishDetailsModal enables confirm button when keywords are present" duration="12"/>
        <testCase name="ConfirmPublishDetailsModal adds keyword only when Enter key is pressed and input is not empty" duration="12"/>
        <testCase name="ConfirmPublishDetailsModal does not add keyword on other key presses" duration="13"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/VesselAndOfficeDropdown.test.tsx">
        <testCase name="VesselAndOfficeDropdown renders placeholder when nothing selected" duration="28"/>
        <testCase name="VesselAndOfficeDropdown renders selected vessel and office names" duration="5"/>
        <testCase name="VesselAndOfficeDropdown opens dropdown and filters options" duration="13"/>
        <testCase name="VesselAndOfficeDropdown calls onChange when vessel is selected" duration="9"/>
        <testCase name="VesselAndOfficeDropdown shows select all/clear all button" duration="11"/>
        <testCase name="VesselAndOfficeDropdown shows &quot;No options found.&quot; when search yields no results" duration="10"/>
        <testCase name="VesselAndOfficeDropdown calls onChange for select all" duration="7"/>
        <testCase name="VesselAndOfficeDropdown calls onChange for clear all" duration="12"/>
        <testCase name="VesselAndOfficeDropdown handles empty options array" duration="6"/>
        <testCase name="VesselAndOfficeDropdown closes dropdown on outside click" duration="18"/>
        <testCase name="VesselAndOfficeDropdown keyboard accessibility: Enter, Space, Escape" duration="50"/>
        <testCase name="VesselAndOfficeDropdown has proper aria attributes" duration="7"/>
        <testCase name="VesselAndOfficeDropdown shows counter and SingleBadgePopover when more than maxDisplayNames selected" duration="2"/>
        <testCase name="VesselAndOfficeDropdown handles grouped options and vessel/office selection" duration="10"/>
        <testCase name="VesselAndOfficeDropdown renders empty group labels but not No options found if groups exist" duration="54"/>
        <testCase name="VesselAndOfficeDropdown does not call onChange in handleSelectAll when no options" duration="67"/>
        <testCase name="VesselAndOfficeDropdown calls onChange with null when last vessel is deselected" duration="16"/>
        <testCase name="VesselAndOfficeDropdown calls onChange with null when last office is deselected" duration="16"/>
        <testCase name="VesselAndOfficeDropdown toggles selection in onChange (add and remove vessel)" duration="120"/>
        <testCase name="VesselAndOfficeDropdown toggles selection in onChange (add and remove office)" duration="35"/>
        <testCase name="VesselAndOfficeDropdown does not close dropdown on outside click if dropdownRef is not set" duration="7"/>
        <testCase name="VesselAndOfficeDropdown covers early return in useLayoutEffect when refs are not set" duration="6"/>
        <testCase name="VesselAndOfficeDropdown covers setMaxDisplayNames logic when namesMeasureRef has no children" duration="8"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/SearchCrewMember.test.tsx">
        <testCase name="SearchCrewMember renders input with placeholder" duration="137"/>
        <testCase name="SearchCrewMember renders with default placeholder when none provided" duration="9"/>
        <testCase name="SearchCrewMember shows dropdown on input focus" duration="43"/>
        <testCase name="SearchCrewMember shows dropdown on wrapper focus" duration="10"/>
        <testCase name="SearchCrewMember filters users based on search input (full_name)" duration="40"/>
        <testCase name="SearchCrewMember filters users based on search input (subText)" duration="9"/>
        <testCase name="SearchCrewMember filters users case-insensitively" duration="10"/>
        <testCase name="SearchCrewMember shows all users when search is empty" duration="15"/>
        <testCase name="SearchCrewMember shows &quot;No users found.&quot; if no match" duration="10"/>
        <testCase name="SearchCrewMember calls onChange and closes dropdown when user is selected" duration="15"/>
        <testCase name="SearchCrewMember clears search input when user is selected" duration="29"/>
        <testCase name="SearchCrewMember closes dropdown when clicking outside" duration="10"/>
        <testCase name="SearchCrewMember closes dropdown when clicking outside UserSelectorDropdown" duration="7"/>
        <testCase name="SearchCrewMember handles empty options array" duration="5"/>
        <testCase name="SearchCrewMember opens dropdown when typing in search input" duration="5"/>
        <testCase name="SearchCrewMember handles onSearch with null value" duration="5"/>
        <testCase name="SearchCrewMember displays user initials correctly" duration="9"/>
        <testCase name="SearchCrewMember displays user details correctly" duration="7"/>
        <testCase name="SearchCrewMember handles user selection with different user IDs" duration="3"/>
        <testCase name="SearchCrewMember cleans up event listeners on unmount" duration="1"/>
        <testCase name="SearchCrewMember handles options with default empty array" duration="4"/>
        <testCase name="SearchCrewMember maintains dropdown state correctly" duration="7"/>
        <testCase name="SearchCrewMember handles search input with special characters" duration="9"/>
        <testCase name="SearchCrewMember handles value prop correctly" duration="2"/>
        <testCase name="SearchCrewMember handles click inside dropdown (should not close)" duration="2"/>
        <testCase name="SearchCrewMember tests UserSelectorDropdown with undefined options (default parameter)" duration="5"/>
        <testCase name="SearchCrewMember covers all branches in event target checking" duration="4"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/ExitPageModal.test.tsx">
        <testCase name="ExitPageModal Component Rendering renders modal with correct title" duration="108"/>
        <testCase name="ExitPageModal Component Rendering renders modal body with warning message" duration="33"/>
        <testCase name="ExitPageModal Component Rendering renders both action buttons" duration="19"/>
        <testCase name="ExitPageModal Component Rendering applies correct CSS classes to modal" duration="12"/>
        <testCase name="ExitPageModal Component Rendering sets correct role attribute on alert" duration="12"/>
        <testCase name="ExitPageModal Modal Properties renders modal as shown by default" duration="22"/>
        <testCase name="ExitPageModal Modal Properties has correct modal size" duration="7"/>
        <testCase name="ExitPageModal Modal Properties has static backdrop" duration="4"/>
        <testCase name="ExitPageModal Modal Properties has correct dialog class name" duration="10"/>
        <testCase name="ExitPageModal Button Interactions calls onConfirm when Exit Page button is clicked" duration="12"/>
        <testCase name="ExitPageModal Button Interactions calls onClose when Assign Approvers button is clicked" duration="6"/>
        <testCase name="ExitPageModal Button Interactions calls onClose when modal is hidden via onHide" duration="6"/>
        <testCase name="ExitPageModal Button Styling applies correct CSS classes to Exit Page button" duration="4"/>
        <testCase name="ExitPageModal Button Styling applies correct CSS classes to Assign Approvers button" duration="4"/>
        <testCase name="ExitPageModal Button Styling has correct button variants" duration="5"/>
        <testCase name="ExitPageModal Modal Body Styling applies correct class to modal body" duration="3"/>
        <testCase name="ExitPageModal Accessibility has proper alert role for warning message" duration="14"/>
        <testCase name="ExitPageModal Accessibility has strong emphasis on warning text" duration="4"/>
        <testCase name="ExitPageModal Accessibility buttons are focusable and clickable" duration="35"/>
        <testCase name="ExitPageModal Component Structure renders modal header with title" duration="4"/>
        <testCase name="ExitPageModal Component Structure renders modal body with alert" duration="5"/>
        <testCase name="ExitPageModal Component Structure renders modal footer with buttons" duration="2"/>
        <testCase name="ExitPageModal Props Handling handles onClose prop correctly" duration="3"/>
        <testCase name="ExitPageModal Props Handling handles onConfirm prop correctly" duration="4"/>
        <testCase name="ExitPageModal Props Handling calls onClose function directly without parameters" duration="3"/>
        <testCase name="ExitPageModal Multiple Interactions handles multiple button clicks correctly" duration="2"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/CustomDatePickerWithRange.test.tsx">
        <testCase name="CustomDatePickerWithRange renders with label and placeholder" duration="23"/>
        <testCase name="CustomDatePickerWithRange displays clear icon when start or end date exists" duration="5"/>
        <testCase name="CustomDatePickerWithRange clears the date range when clear icon is clicked" duration="7"/>
        <testCase name="CustomDatePickerWithRange does not show clear icon when no dates selected" duration="7"/>
        <testCase name="CustomDatePickerWithRange applies aria-label for accessibility" duration="6"/>
        <testCase name="CustomDatePickerWithRange handles date selection and formats dates correctly" duration="5"/>
        <testCase name="CustomDatePickerWithRange handles null date values correctly" duration="3"/>
        <testCase name="CustomDatePickerWithRange prevents keyboard input" duration="6"/>
        <testCase name="CustomDatePickerWithRange respects min and max date constraints" duration="3"/>
        <testCase name="CustomDatePickerWithRange clear button has appropriate accessibility label" duration="3"/>
        <testCase name="CustomDatePickerWithRange calendar icon has appropriate accessibility label" duration="4"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/SelectableCheckboxGrid.test.tsx">
        <testCase name="SelectableCheckboxGrid renders title, subtitle, and checkboxes" duration="28"/>
        <testCase name="SelectableCheckboxGrid calls onChange when a checkbox is selected or unselected" duration="35"/>
        <testCase name="SelectableCheckboxGrid filters unchecked options based on search input" duration="26"/>
        <testCase name="SelectableCheckboxGrid renders and handles &quot;Others&quot; checkbox and input when selected" duration="27"/>
        <testCase name="SelectableCheckboxGrid renders &quot;Others&quot; checkbox when not selected" duration="22"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/utils/svgIcons.test.tsx">
        <testCase name="SVG Icons SortIcon renders correctly" duration="20"/>
        <testCase name="SVG Icons RoundCheckFilled renders correctly" duration="4"/>
        <testCase name="SVG Icons CheckFilled renders correctly" duration="1"/>
        <testCase name="SVG Icons CheckUnFilled renders correctly" duration="0"/>
        <testCase name="SVG Icons CommentIcon renders correctly" duration="3"/>
        <testCase name="SVG Icons InfoIcon renders correctly" duration="2"/>
        <testCase name="SVG Icons CalendarIcon renders correctly" duration="5"/>
        <testCase name="SVG Icons DeleteJobIcon renders correctly" duration="3"/>
        <testCase name="SVG Icons JobCardArrowUpIcon renders correctly" duration="1"/>
        <testCase name="SVG Icons JobCardArrowDownIcon renders correctly" duration="0"/>
        <testCase name="SVG Icons EditFormDetailIcon renders correctly" duration="1"/>
        <testCase name="SVG Icons ActionMenuIcon renders correctly" duration="8"/>
        <testCase name="SVG Icons RadioUncheckedIcon renders correctly" duration="4"/>
        <testCase name="SVG Icons RadioCheckedIcon renders correctly" duration="2"/>
        <testCase name="SVG Icons JobAlertIcon renders correctly" duration="2"/>
        <testCase name="SVG Icons CrewIcon renders correctly" duration="2"/>
        <testCase name="SVG Icons ExclaimationIcon renders correctly" duration="1"/>
        <testCase name="SVG Icons ProfileIcon renders correctly" duration="3"/>
        <testCase name="SVG Icons CrossIcon renders correctly" duration="4"/>
        <testCase name="SVG Icons Icons accept and pass through props" duration="3"/>
        <testCase name="SVG Icons Icons that support props accept custom props and spread them correctly" duration="14"/>
        <testCase name="SVG Icons Icons render without props" duration="4"/>
        <testCase name="SVG Icons Icons maintain their default dimensions when no size props provided" duration="2"/>
        <testCase name="SVG Icons Icons with props support accept custom dimensions" duration="17"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/FormCheckRadio.test.tsx">
        <testCase name="FormCheckRadio renders unchecked radio button" duration="12"/>
        <testCase name="FormCheckRadio renders checked radio button" duration="1"/>
        <testCase name="FormCheckRadio renders the label when provided" duration="1"/>
        <testCase name="FormCheckRadio does not render label when not provided" duration="7"/>
        <testCase name="FormCheckRadio applies disabled styling when disabled" duration="7"/>
        <testCase name="FormCheckRadio applies pointer cursor when not disabled" duration="7"/>
        <testCase name="FormCheckRadio applies default pointer cursor when disabled prop is not provided" duration="19"/>
        <testCase name="FormCheckRadio passes disabled prop to input element" duration="4"/>
        <testCase name="FormCheckRadio handles onChange event" duration="7"/>
        <testCase name="FormCheckRadio applies custom className" duration="1"/>
        <testCase name="FormCheckRadio uses custom id when provided" duration="2"/>
        <testCase name="FormCheckRadio uses default id when not provided" duration="1"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/SearchUserDropdown.test.tsx">
        <testCase name="SearchUserDropdown renders with placeholder when no users are selected" duration="15"/>
        <testCase name="SearchUserDropdown opens dropdown on click and displays all users" duration="21"/>
        <testCase name="SearchUserDropdown filters users based on search input" duration="22"/>
        <testCase name="SearchUserDropdown calls onChange when a user is selected" duration="19"/>
        <testCase name="SearchUserDropdown closes dropdown when clicking outside" duration="37"/>
        <testCase name="SearchUserDropdown Custom placeholder renders with custom placeholder" duration="9"/>
        <testCase name="SearchUserDropdown Selected users display displays selected user names" duration="5"/>
        <testCase name="SearchUserDropdown Selected users display displays counter when more than maxDisplayNames are selected" duration="12"/>
        <testCase name="SearchUserDropdown Selected users display shows tooltip with all selected user names" duration="4"/>
        <testCase name="SearchUserDropdown User deselection deselects a user when clicked again" duration="20"/>
        <testCase name="SearchUserDropdown Select all functionality deselects all when all are already selected" duration="45"/>
        <testCase name="SearchUserDropdown Select all functionality selects only filtered users when search is active" duration="31"/>
        <testCase name="SearchUserDropdown Search functionality filters users by email" duration="38"/>
        <testCase name="SearchUserDropdown Search functionality filters users by designation" duration="11"/>
        <testCase name="SearchUserDropdown Search functionality shows &quot;No users found&quot; when search returns no results" duration="14"/>
        <testCase name="SearchUserDropdown Search functionality handles case-insensitive search" duration="6"/>
        <testCase name="SearchUserDropdown Keyboard accessibility opens dropdown on Enter key" duration="11"/>
        <testCase name="SearchUserDropdown Keyboard accessibility opens dropdown on Space key" duration="11"/>
        <testCase name="SearchUserDropdown Keyboard accessibility closes dropdown on Escape key" duration="5"/>
        <testCase name="SearchUserDropdown User initials generation displays correct initials for users" duration="18"/>
        <testCase name="SearchUserDropdown Edge cases handles empty options array" duration="13"/>
        <testCase name="SearchUserDropdown Edge cases handles users without designation" duration="9"/>
        <testCase name="SearchUserDropdown Edge cases handles invalid user IDs in value prop" duration="5"/>
        <testCase name="SearchUserDropdown Edge cases handles undefined/null search values" duration="11"/>
        <testCase name="SearchUserDropdown Component lifecycle does not call onChange when component mounts with selected users" duration="6"/>
        <testCase name="SearchUserDropdown Component lifecycle handles window resize events" duration="5"/>
        <testCase name="SearchUserDropdown Accessibility has proper ARIA attributes" duration="9"/>
        <testCase name="SearchUserDropdown Accessibility updates aria-expanded when dropdown opens" duration="25"/>
        <testCase name="SearchUserDropdown Accessibility has proper checkbox roles and states" duration="35"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/hooks/useQuery.test.tsx">
        <testCase name="useQuery Basic functionality should fetch data successfully and show loading states" duration="36"/>
        <testCase name="useQuery Basic functionality should not fetch when enabled is false" duration="3"/>
        <testCase name="useQuery Basic functionality should refetch data when refetch is called" duration="32"/>
        <testCase name="useQuery Query key handling should handle string query keys" duration="16"/>
        <testCase name="useQuery Query key handling should handle array query keys" duration="18"/>
        <testCase name="useQuery Query key handling should refetch when query key changes" duration="10"/>
        <testCase name="useQuery Options handling should respect staleTime option" duration="12"/>
        <testCase name="useQuery Options handling should refetch in background when staleTime is 0" duration="18"/>
        <testCase name="useQuery Options handling should handle cache cleanup with cacheTime" duration="12"/>
        <testCase name="useQuery Error handling and edge cases should handle component unmounting during fetch and prevent state updates" duration="5"/>
        <testCase name="useQuery Error handling and edge cases should handle query function that returns null" duration="9"/>
        <testCase name="useQuery Error handling and edge cases should handle complex query keys with objects" duration="14"/>
        <testCase name="useQuery State transitions should transition from idle to loading to success" duration="8"/>
        <testCase name="useQuery Helper functions should provide correct boolean flags" duration="6"/>
        <testCase name="useQuery Retry logic and error handling should handle retry configuration options" duration="5"/>
        <testCase name="useQuery Retry logic and error handling should handle error callbacks configuration" duration="6"/>
        <testCase name="useQuery Retry logic and error handling should handle retry configuration with successful queries" duration="6"/>
        <testCase name="useQuery Retry logic and error handling should handle retry delay configuration" duration="14"/>
        <testCase name="useQuery Error state management should handle error state configuration" duration="11"/>
        <testCase name="useQuery Error state management should handle refetch functionality" duration="9"/>
        <testCase name="useQuery Error state management should handle error state initialization" duration="34"/>
        <testCase name="useQuery Error state management should handle error state management configuration" duration="24"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/SearchInput.test.tsx">
        <testCase name="SearchInput renders with custom placeholder" duration="34"/>
        <testCase name="SearchInput calls onSearch with null when input is empty or whitespace only" duration="15"/>
        <testCase name="SearchInput calls onSearch with cleaned value (removes % and _)" duration="10"/>
        <testCase name="SearchInput passes disabled prop to input" duration="9"/>
        <testCase name="SearchInput reflects the controlled value prop" duration="24"/>
        <testCase name="SearchInput shows clear button when showClear is true" duration="9"/>
        <testCase name="SearchInput does not show clear button when showClear is false" duration="2"/>
        <testCase name="SearchInput does not show clear button by default" duration="3"/>
        <testCase name="SearchInput calls onSearch with empty string when clear button is clicked" duration="4"/>
        <testCase name="SearchInput renders with default placeholder when not provided" duration="2"/>
        <testCase name="SearchInput renders search icon" duration="1"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftsListing.test.tsx">
        <testCase name="RADraftsListing Component Component Rendering renders the component correctly" duration="42"/>
        <testCase name="RADraftsListing Component Component Rendering renders with correct initial state" duration="13"/>
        <testCase name="RADraftsListing Component Component Rendering renders table columns correctly" duration="2"/>
        <testCase name="RADraftsListing Component Component Rendering displays loading state correctly" duration="1"/>
        <testCase name="RADraftsListing Component Component Rendering displays fetching next page state correctly" duration="6"/>
        <testCase name="RADraftsListing Component Tab Switching switches to RA Template tab correctly" duration="19"/>
        <testCase name="RADraftsListing Component Tab Switching switches back to Risk Assessment tab correctly" duration="13"/>
        <testCase name="RADraftsListing Component Draft Actions renders action dropdown for each draft" duration="4"/>
        <testCase name="RADraftsListing Component Draft Actions displays correct action buttons text" duration="4"/>
        <testCase name="RADraftsListing Component Draft Actions shows action dropdown structure correctly" duration="9"/>
        <testCase name="RADraftsListing Component Draft Actions handles edit action correctly for risk assessment tab" duration="16"/>
        <testCase name="RADraftsListing Component Draft Actions handles edit action correctly for template tab" duration="5"/>
        <testCase name="RADraftsListing Component Draft Actions handles discard action correctly" duration="22"/>
        <testCase name="RADraftsListing Component Draft Actions handles discard action for second draft correctly" duration="15"/>
        <testCase name="RADraftsListing Component Modal Interactions does not show modal initially" duration="3"/>
        <testCase name="RADraftsListing Component Modal Interactions shows modal when discard is clicked" duration="48"/>
        <testCase name="RADraftsListing Component Modal Interactions handles modal close with refetch correctly" duration="14"/>
        <testCase name="RADraftsListing Component Modal Interactions handles modal close without refetch correctly" duration="47"/>
        <testCase name="RADraftsListing Component Modal Interactions shows dropdown menus correctly" duration="3"/>
        <testCase name="RADraftsListing Component Modal Interactions renders three dots icon correctly" duration="4"/>
        <testCase name="RADraftsListing Component Modal Interactions renders dropdown structure correctly" duration="3"/>
        <testCase name="RADraftsListing Component Table Functionality calls fetchNextPage when load more is clicked" duration="5"/>
        <testCase name="RADraftsListing Component Table Functionality displays correct sorting information" duration="4"/>
        <testCase name="RADraftsListing Component Table Functionality handles empty data correctly" duration="3"/>
        <testCase name="RADraftsListing Component Data Display displays draft data correctly" duration="18"/>
        <testCase name="RADraftsListing Component Data Display displays action dropdowns for each draft" duration="7"/>
        <testCase name="RADraftsListing Component Data Display displays table rows correctly" duration="14"/>
        <testCase name="RADraftsListing Component Hook Integration calls useInfiniteQuery with correct parameters for risk tab" duration="29"/>
        <testCase name="RADraftsListing Component Hook Integration uses correct fetch function for risk tab" duration="13"/>
        <testCase name="RADraftsListing Component Hook Integration uses correct fetch function for template tab" duration="10"/>
        <testCase name="getColumns Function Column Configuration returns correct number of columns" duration="0"/>
        <testCase name="getColumns Function Column Configuration configures task_requiring_ra column correctly" duration="1"/>
        <testCase name="getColumns Function Column Configuration configures updated_at column correctly" duration="0"/>
        <testCase name="getColumns Function Column Configuration configures action column correctly" duration="3"/>
        <testCase name="getColumns Function Column Configuration updates header with different total counts" duration="0"/>
        <testCase name="getColumns Function Column Configuration handles zero total count" duration="0"/>
        <testCase name="getColumns Function Cell Renderers renders task_requiring_ra cell correctly" duration="0"/>
        <testCase name="getColumns Function Cell Renderers renders updated_at cell correctly" duration="0"/>
        <testCase name="getColumns Function Cell Renderers renders action cell correctly" duration="0"/>
        <testCase name="getColumns Function Action Handlers generates correct navigation path for risk tab" duration="0"/>
        <testCase name="getColumns Function Action Handlers generates correct navigation path for template tab" duration="0"/>
        <testCase name="getColumns Function Action Handlers calls handleDiscardDraft when discard is clicked" duration="0"/>
        <testCase name="getColumns Function Column Dependencies works with different selectedTab values" duration="0"/>
        <testCase name="getColumns Function Column Dependencies passes all required parameters to column functions" duration="4"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/BottomButton.test.tsx">
        <testCase name="BottomButton renders the correct number of buttons" duration="40"/>
        <testCase name="BottomButton renders button titles correctly" duration="4"/>
        <testCase name="BottomButton calls onClick handler when button is clicked" duration="5"/>
        <testCase name="BottomButton does not call onClick handler when disabled button is clicked" duration="2"/>
        <testCase name="BottomButton applies custom class correctly" duration="1"/>
        <testCase name="BottomButton applies the correct button type and variant" duration="1"/>
        <testCase name="BottomButton applies disabled attribute when specified" duration="7"/>
        <testCase name="BottomButton uses &quot;primary&quot; as default variant when none is provided" duration="2"/>
        <testCase name="BottomButton uses &quot;button&quot; as default type when none is provided" duration="3"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RADrafts/RADraftHeader.test.tsx">
        <testCase name="RADraftHeader Basic Rendering renders the component without crashing" duration="59"/>
        <testCase name="RADraftHeader Basic Rendering renders breadcrumb navigation correctly" duration="5"/>
        <testCase name="RADraftHeader Basic Rendering renders tab buttons correctly" duration="37"/>
        <testCase name="RADraftHeader Basic Rendering applies correct container classes" duration="3"/>
        <testCase name="RADraftHeader Basic Rendering applies correct breadcrumb container classes" duration="9"/>
        <testCase name="RADraftHeader Basic Rendering applies correct tab container classes" duration="5"/>
        <testCase name="RADraftHeader Tab Functionality shows Risk Assessment tab as active when activeTab is 1" duration="10"/>
        <testCase name="RADraftHeader Tab Functionality shows RA Template tab as active when activeTab is 2" duration="50"/>
        <testCase name="RADraftHeader Tab Functionality calls setActiveTab with 1 when Risk Assessment tab is clicked" duration="64"/>
        <testCase name="RADraftHeader Tab Functionality calls setActiveTab with 2 when RA Template tab is clicked" duration="9"/>
        <testCase name="RADraftHeader Tab Functionality handles multiple tab clicks correctly" duration="17"/>
        <testCase name="RADraftHeader Button Properties applies correct variant to tab buttons" duration="30"/>
        <testCase name="RADraftHeader Button Properties applies draft-listing-tab class to both buttons" duration="42"/>
        <testCase name="RADraftHeader Edge Cases handles activeTab value of 0 correctly" duration="16"/>
        <testCase name="RADraftHeader Edge Cases handles activeTab value greater than 2 correctly" duration="7"/>
        <testCase name="RADraftHeader Edge Cases handles negative activeTab value correctly" duration="34"/>
        <testCase name="RADraftHeader Accessibility has proper button roles" duration="37"/>
        <testCase name="RADraftHeader Accessibility has proper link role for breadcrumb" duration="9"/>
        <testCase name="RADraftHeader Component Structure maintains proper DOM hierarchy" duration="2"/>
        <testCase name="RADraftHeader Component Structure contains both tabs within tab container" duration="35"/>
        <testCase name="RADraftHeader Props Validation works with different setActiveTab functions" duration="27"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/UsernameProfile.test.tsx">
        <testCase name="UsernameProfile renders username and subtext correctly" duration="21"/>
        <testCase name="UsernameProfile displays initials in avatar" duration="3"/>
        <testCase name="UsernameProfile renders with correct CSS classes" duration="16"/>
        <testCase name="UsernameProfile handles single name correctly" duration="6"/>
        <testCase name="UsernameProfile handles empty username" duration="1"/>
        <testCase name="UsernameProfile handles empty subText" duration="1"/>
        <testCase name="UsernameProfile handles long names correctly" duration="1"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/Drawer.test.tsx">
        <testCase name="Drawer component renders trigger and toggles drawer open/close" duration="35"/>
        <testCase name="Drawer component renders children as function and allows closing via context" duration="9"/>
        <testCase name="Drawer component applies custom className and position" duration="4"/>
        <testCase name="Drawer component hides content when closed via close button" duration="17"/>
        <testCase name="Drawer component Portal behavior renders drawer in portal by default" duration="4"/>
        <testCase name="Drawer component Portal behavior renders drawer without portal when notUsePortal is true" duration="4"/>
        <testCase name="Drawer component Position variants applies end position by default" duration="9"/>
        <testCase name="Drawer component Position variants applies start position when specified" duration="7"/>
        <testCase name="Drawer component Drawer structure and styling has correct CSS classes and structure" duration="24"/>
        <testCase name="Drawer component Drawer structure and styling applies custom className correctly" duration="5"/>
        <testCase name="Drawer component Drawer structure and styling renders close icon with correct styling" duration="5"/>
        <testCase name="Drawer component Children rendering renders static children correctly" duration="17"/>
        <testCase name="Drawer component Children rendering renders function children with closeDrawer prop" duration="12"/>
        <testCase name="Drawer component Children rendering does not render children when drawer is closed" duration="2"/>
        <testCase name="Drawer component Trigger behavior clones trigger element with onClick handler" duration="5"/>
        <testCase name="Drawer component Trigger behavior handles trigger with existing props" duration="6"/>
        <testCase name="Drawer component State management toggles open state correctly" duration="4"/>
        <testCase name="Drawer component State management handles transition end event when drawer is open" duration="3"/>
        <testCase name="Drawer component Edge cases handles empty title" duration="3"/>
        <testCase name="Drawer component Edge cases handles undefined children" duration="4"/>
        <testCase name="Drawer component Edge cases handles null children" duration="3"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/icons/reassign-user-icon.test.tsx">
        <testCase name="SvgUserOutline (reassign-user-icon) renders an SVG element with correct attributes" duration="24"/>
        <testCase name="SvgUserOutline (reassign-user-icon) spreads additional props to the svg element" duration="1"/>
        <testCase name="SvgUserOutline (reassign-user-icon) renders all expected &lt;path&gt; elements" duration="1"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/CreateRA/BasicDetails.test.tsx">
        <testCase name="BasicDetails Template Form Rendering renders basic template form fields" duration="39"/>
        <testCase name="BasicDetails Template Form Rendering handles input changes for template form" duration="48"/>
        <testCase name="BasicDetails Template Form Rendering handles blur events and sets touched state" duration="13"/>
        <testCase name="BasicDetails Template Form Rendering validates required fields for template form" duration="40"/>
        <testCase name="BasicDetails Template Form Rendering passes validation for complete template form" duration="6"/>
        <testCase name="BasicDetails Risk Form Rendering renders risk form with additional fields" duration="22"/>
        <testCase name="BasicDetails Risk Form Rendering handles assessor dropdown changes" duration="12"/>
        <testCase name="BasicDetails Risk Form Rendering handles vessel/office dropdown changes" duration="12"/>
        <testCase name="BasicDetails Risk Form Rendering handles date changes" duration="13"/>
        <testCase name="BasicDetails Risk Form Rendering handles approval required changes" duration="47"/>
        <testCase name="BasicDetails Risk Form Rendering validates required fields for risk form" duration="9"/>
        <testCase name="BasicDetails Risk Form Rendering passes validation for complete risk form" duration="26"/>
        <testCase name="BasicDetails Validation Logic validates individual fields correctly" duration="8"/>
        <testCase name="BasicDetails Validation Logic calls onValidate callback when validation state changes" duration="7"/>
        <testCase name="BasicDetails Validation Logic handles validation without onValidate callback" duration="12"/>
        <testCase name="BasicDetails Form Handlers handles dropdown changes correctly" duration="10"/>
        <testCase name="BasicDetails Form Handlers handles blur events for dropdowns" duration="11"/>
        <testCase name="BasicDetails Edge Cases handles undefined form values gracefully" duration="5"/>
        <testCase name="BasicDetails Edge Cases handles null values in risk form" duration="4"/>
        <testCase name="BasicDetails Edge Cases handles empty approval_required array" duration="11"/>
        <testCase name="BasicDetails Edge Cases handles switching between assessor types" duration="13"/>
        <testCase name="BasicDetails Edge Cases handles template type with default props" duration="4"/>
        <testCase name="BasicDetails Accessibility has proper form labels and controls" duration="6"/>
        <testCase name="BasicDetails Accessibility shows proper error messages with invalid feedback" duration="5"/>
        <testCase name="BasicDetails Performance does not cause unnecessary re-renders" duration="2"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/BadgeListPopover.test.tsx">
        <testCase name="BadgeListPopover renders the container with correct class" duration="22"/>
        <testCase name="BadgeListPopover renders individual badges with correct class and content when space allows" duration="2"/>
        <testCase name="BadgeListPopover handles empty badges array" duration="1"/>
        <testCase name="BadgeListPopover shows &quot;+X more&quot; badge when there are remaining badges" duration="3"/>
        <testCase name="BadgeListPopover popover shows remaining badges when hovering over &quot;+X more&quot;" duration="56"/>
        <testCase name="BadgeListPopover handles ResizeObserver lifecycle correctly" duration="4"/>
        <testCase name="BadgeListPopover component renders without crashing with various badge counts" duration="4"/>
        <testCase name="BadgeListPopover handles badges with special characters correctly" duration="1"/>
        <testCase name="BadgeListPopover handles very long badge names without breaking" duration="1"/>
        <testCase name="BadgeListPopover maintains component structure with different badge configurations" duration="1"/>
        <testCase name="BadgeListPopover handles container ref correctly" duration="1"/>
        <testCase name="BadgeListPopover renders without errors when no badges are provided" duration="1"/>
        <testCase name="BadgeListPopover popover contains correct content when triggered" duration="31"/>
        <testCase name="BadgeListPopover handles badge key generation correctly" duration="18"/>
        <testCase name="BadgeListPopover responds to ResizeObserver changes" duration="5"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/CheckboxComponent.test.tsx">
        <testCase name="CheckboxComponent Rendering renders label correctly" duration="26"/>
        <testCase name="CheckboxComponent Rendering renders CheckFilled icon when checked is true" duration="4"/>
        <testCase name="CheckboxComponent Rendering renders CheckUnFilled icon when checked is false" duration="4"/>
        <testCase name="CheckboxComponent Rendering renders without label when label is not provided" duration="2"/>
        <testCase name="CheckboxComponent Rendering renders with React node as label" duration="4"/>
        <testCase name="CheckboxComponent Rendering has correct CSS classes and structure" duration="3"/>
        <testCase name="CheckboxComponent Rendering contains hidden input element with correct attributes" duration="10"/>
        <testCase name="CheckboxComponent Rendering hidden input reflects unchecked state" duration="1"/>
        <testCase name="CheckboxComponent Click Interactions calls onChange when label is clicked" duration="3"/>
        <testCase name="CheckboxComponent Click Interactions stops propagation on label click" duration="1"/>
        <testCase name="CheckboxComponent Click Interactions stops propagation on direct label element click" duration="3"/>
        <testCase name="CheckboxComponent Click Interactions stops propagation on hidden input click" duration="2"/>
        <testCase name="CheckboxComponent Keyboard Interactions calls onChange when Enter key is pressed on label" duration="4"/>
        <testCase name="CheckboxComponent Keyboard Interactions calls onChange when Space key is pressed on label" duration="2"/>
        <testCase name="CheckboxComponent Keyboard Interactions does not call onChange when other keys are pressed on label" duration="1"/>
        <testCase name="CheckboxComponent Keyboard Interactions does not call onChange when non-interactive keys are pressed on hidden input" duration="2"/>
        <testCase name="CheckboxComponent Keyboard Interactions stops propagation on input change events" duration="1"/>
        <testCase name="CheckboxComponent Accessibility has correct accessibility attributes when checked" duration="1"/>
        <testCase name="CheckboxComponent Accessibility has correct accessibility attributes when unchecked" duration="2"/>
        <testCase name="CheckboxComponent Accessibility has correct label association" duration="1"/>
        <testCase name="CheckboxComponent Accessibility label is properly associated with input" duration="2"/>
        <testCase name="CheckboxComponent Accessibility works without label" duration="2"/>
        <testCase name="CheckboxComponent Accessibility handles React node labels" duration="3"/>
        <testCase name="CheckboxComponent Accessibility is focusable and accessible" duration="5"/>
        <testCase name="CheckboxComponent Edge Cases handles multiple rapid clicks correctly" duration="5"/>
        <testCase name="CheckboxComponent Edge Cases handles multiple click events" duration="6"/>
        <testCase name="CheckboxComponent Edge Cases maintains visual consistency with label styling" duration="5"/>
        <testCase name="CheckboxComponent Edge Cases handles empty string label" duration="2"/>
        <testCase name="CheckboxComponent Edge Cases handles special characters in id" duration="15"/>
        <testCase name="CheckboxComponent State Changes updates icon when checked state changes" duration="12"/>
        <testCase name="CheckboxComponent State Changes updates checked state when checked prop changes" duration="4"/>
        <testCase name="CheckboxComponent State Changes updates hidden input checked state" duration="18"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/RiskReasonCell.test.tsx">
        <testCase name="RiskReasonCell renders four rows for parameter_type_id 1-4" duration="24"/>
        <testCase name="RiskReasonCell renders reasons for matching parameter_type_id" duration="7"/>
        <testCase name="RiskReasonCell renders &lt;hr&gt; between rows except after the last" duration="18"/>
        <testCase name="RiskReasonCell renders correct order and keys" duration="1"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/icons/close-icon.test.tsx">
        <testCase name="CloseIcon renders without crashing" duration="19"/>
        <testCase name="CloseIcon applies passed props correctly" duration="3"/>
        <testCase name="CloseIcon has correct width, height and viewBox attributes" duration="0"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/root.component.test.tsx">
        <testCase name="Root Component renders loading spinner while initializing user service" duration="41"/>
        <testCase name="Root Component renders loading spinner while roleConfig is null" duration="12"/>
        <testCase name="Root Component renders main app structure after loading" duration="7"/>
        <testCase name="Root Component calls ga4EventTrigger successfully" duration="6"/>
        <testCase name="Root Component handles ga4EventTrigger errors gracefully" duration="76"/>
        <testCase name="Root Component renders loading spinner with correct accessibility attributes" duration="31"/>
        <testCase name="Root Component handles missing ga4react gracefully" duration="9"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/SingleBadgePopover.test.tsx">
        <testCase name="SingleBadgePopover renders the badge label" duration="143"/>
        <testCase name="SingleBadgePopover shows popover with items on hover" duration="53"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RATemplateListing/TemplateListing.test.tsx">
        <testCase name="TemplateListing renders header, filters, mostly used card, and table" duration="24"/>
        <testCase name="TemplateListing renders table with correct sorting callback" duration="1"/>
        <testCase name="getColumns renders Task Required cell" duration="0"/>
        <testCase name="getColumns renders No. of Risk Categories cell with values" duration="1"/>
        <testCase name="getColumns renders No. of Risk Categories cell with empty array" duration="0"/>
        <testCase name="getColumns renders No. of Hazard Categories cell with values" duration="0"/>
        <testCase name="getColumns renders No. of Hazard Categories cell with empty array" duration="0"/>
        <testCase name="getColumns renders Created on cell" duration="2"/>
        <testCase name="getColumns renders Created by cell with user" duration="0"/>
        <testCase name="getColumns renders Created by cell with missing user" duration="2"/>
        <testCase name="getColumns renders Keywords cell with values" duration="4"/>
        <testCase name="getColumns renders Keywords cell with empty array" duration="1"/>
        <testCase name="getColumns renders Action cell" duration="2"/>
        <testCase name="getColumns renders Action cell with missing user" duration="1"/>
        <testCase name="TemplateListing integration calls sorting callback and resets sorting when empty array is passed" duration="10"/>
        <testCase name="TemplateListing integration calls sorting callback with new sorting value" duration="62"/>
        <testCase name="TemplateListing integration calls handleFilterChange when filter is changed" duration="10"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/icons/three-dot-menu-icon.test.tsx">
        <testCase name="ThreeDotsMenu renders with default size and color" duration="25"/>
        <testCase name="ThreeDotsMenu applies custom width, height, and color" duration="2"/>
        <testCase name="ThreeDotsMenu applies custom className" duration="4"/>
        <testCase name="ThreeDotsMenu triggers onClick when clicked" duration="7"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/InputComponent.test.tsx">
        <testCase name="InputComponent renders label and input with correct props" duration="57"/>
        <testCase name="InputComponent renders a textarea when type is textarea" duration="4"/>
        <testCase name="InputComponent calls onChange and onBlur handlers" duration="6"/>
        <testCase name="InputComponent displays helpText and error messages correctly" duration="1"/>
        <testCase name="InputComponent applies disabled attribute" duration="1"/>
        <testCase name="InputComponent renders max length counter when showMaxLength is true" duration="2"/>
        <testCase name="InputComponent applies custom className and style" duration="14"/>
        <testCase name="InputComponent uses custom classes from classes prop" duration="4"/>
        <testCase name="InputComponent renders with different input types" duration="13"/>
        <testCase name="InputComponent handles empty form object" duration="1"/>
        <testCase name="InputComponent handles null form object" duration="1"/>
        <testCase name="InputComponent handles undefined form object" duration="1"/>
        <testCase name="InputComponent renders with custom formControlId" duration="1"/>
        <testCase name="InputComponent uses name as controlId when formControlId is null" duration="1"/>
        <testCase name="InputComponent renders without onBlur handler" duration="1"/>
        <testCase name="InputComponent handles maxLength as undefined" duration="1"/>
        <testCase name="InputComponent handles maxLength as null" duration="2"/>
        <testCase name="InputComponent renders with empty helpText" duration="6"/>
        <testCase name="InputComponent renders with null helpText" duration="3"/>
        <testCase name="InputComponent renders with undefined helpText" duration="1"/>
        <testCase name="InputComponent renders with empty error" duration="1"/>
        <testCase name="InputComponent renders with null error" duration="1"/>
        <testCase name="InputComponent renders with undefined error" duration="1"/>
        <testCase name="InputComponent applies isInvalid when error is truthy" duration="1"/>
        <testCase name="InputComponent renders max length counter with empty form value" duration="1"/>
        <testCase name="InputComponent renders max length counter with null form value" duration="3"/>
        <testCase name="InputComponent renders max length counter with undefined form value" duration="2"/>
        <testCase name="InputComponent renders without classes prop" duration="1"/>
        <testCase name="InputComponent renders with partial classes object" duration="2"/>
        <testCase name="InputComponent combines custom className with classes.component" duration="4"/>
        <testCase name="InputComponent handles textarea with default rows when rows prop is not provided" duration="8"/>
        <testCase name="InputComponent renders textarea with custom rows" duration="1"/>
        <testCase name="InputComponent does not apply rows attribute to non-textarea inputs" duration="1"/>
        <testCase name="InputComponent uses default values when props are not provided" duration="3"/>
        <testCase name="InputComponent uses default className when not provided" duration="1"/>
        <testCase name="InputComponent uses default style when not provided" duration="2"/>
        <testCase name="InputComponent uses default disabled state when not provided" duration="1"/>
        <testCase name="InputComponent uses default type when not provided" duration="4"/>
        <testCase name="InputComponent uses default helpText when not provided" duration="1"/>
        <testCase name="InputComponent uses default error when not provided" duration="3"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/RiskJobCell.test.tsx">
        <testCase name="RiskJobCell renders job step text" duration="20"/>
        <testCase name="RiskJobCell shows alert icon when risk type has residual risk rating with reason" duration="2"/>
        <testCase name="RiskJobCell shows alert icon when template type has residual risk rating with reason" duration="1"/>
        <testCase name="RiskJobCell does not show alert icon when no residual risk rating" duration="1"/>
        <testCase name="RiskJobCell does not show alert icon when residual risk rating has no reason" duration="1"/>
        <testCase name="RiskJobCell does not show alert icon when residual risk rating is empty array" duration="1"/>
        <testCase name="RiskJobCell handles multiple risk ratings correctly" duration="1"/>
        <testCase name="RiskJobCell renders with correct CSS classes" duration="2"/>
        <testCase name="RiskJobCell handles undefined residual risk rating" duration="6"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/UnitSelectInputComponent.test.tsx">
        <testCase name="UnitSelectInputComponent renders label and input with default value" duration="33"/>
        <testCase name="UnitSelectInputComponent renders unit options in select dropdown" duration="9"/>
        <testCase name="UnitSelectInputComponent calls onChange when input value changes" duration="7"/>
        <testCase name="UnitSelectInputComponent calls onChange when select value changes" duration="8"/>
        <testCase name="UnitSelectInputComponent displays error message if error prop is passed" duration="9"/>
        <testCase name="UnitSelectInputComponent displays help text and max length counter" duration="4"/>
        <testCase name="UnitSelectInputComponent respects disabled prop on input" duration="3"/>
        <testCase name="UnitSelectInputComponent renders with custom placeholder" duration="2"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RATemplateListing/components/TemplateListingFilters.test.tsx">
        <testCase name="TemplateListingFilters renders all filter controls" duration="78"/>
        <testCase name="TemplateListingFilters calls onFilterChange for search input" duration="10"/>
        <testCase name="TemplateListingFilters calls onFilterChange for user dropdown" duration="4"/>
        <testCase name="TemplateListingFilters calls onFilterChange for date picker" duration="4"/>
        <testCase name="TemplateListingFilters handles error in useEffect" duration="56"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/icons/plus-icon.test.tsx">
        <testCase name="PlusIcon renders with default size and color" duration="23"/>
        <testCase name="PlusIcon applies custom width and height" duration="2"/>
        <testCase name="PlusIcon applies custom color" duration="2"/>
        <testCase name="PlusIcon applies custom className and style" duration="27"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/icons/index.test.tsx">
        <testCase name="Icons Index exports PlusIcon component" duration="2"/>
        <testCase name="Icons Index exports ThreeDotsMenuIcon component" duration="0"/>
        <testCase name="Icons Index exports CloseIcon component" duration="1"/>
        <testCase name="Icons Index exports CalendarIcon component" duration="0"/>
        <testCase name="Icons Index exports TrashIcon component" duration="0"/>
        <testCase name="Icons Index exports ExternalLinkIcon component" duration="1"/>
        <testCase name="Icons Index all exported components are React components" duration="1"/>
        <testCase name="Icons Index can render all exported components" duration="24"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/CustomDatePicker.test.tsx">
        <testCase name="CustomDatePicker renders with label and placeholder" duration="36"/>
        <testCase name="CustomDatePicker renders clear icon when value is present and clears on click" duration="19"/>
        <testCase name="CustomDatePicker shows calendar icon when no value and not required" duration="4"/>
        <testCase name="CustomDatePicker applies aria-label and correct id" duration="4"/>
        <testCase name="CustomDatePicker displays custom error message when provided" duration="24"/>
        <testCase name="CustomDatePicker prevents keyboard input" duration="6"/>
        <testCase name="CustomDatePicker renders with min and max date constraints" duration="11"/>
        <testCase name="CustomDatePicker handles date selection" duration="5"/>
        <testCase name="CustomDatePicker calendar icon is not visible when value is present" duration="8"/>
        <testCase name="CustomDatePicker clear button is accessible" duration="4"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/RiskRatingCell.test.tsx">
        <testCase name="RiskRatingCell renders all parameter labels (P, E, A, R)" duration="44"/>
        <testCase name="RiskRatingCell shows &quot;Not Selected&quot; when no rating is provided" duration="62"/>
        <testCase name="RiskRatingCell renders correct risk labels and styles for provided ratings" duration="31"/>
        <testCase name="RiskRatingCell renders horizontal rules between rows except after the last" duration="11"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/ColoredTile.test.tsx">
        <testCase name="ColoredTile renders with default props" duration="19"/>
        <testCase name="ColoredTile renders with all theme variants" duration="6"/>
        <testCase name="ColoredTile renders with custom className" duration="2"/>
        <testCase name="ColoredTile renders with different text content" duration="26"/>
        <testCase name="ColoredTile renders as span element" duration="1"/>
        <testCase name="ColoredTile combines multiple class names correctly" duration="0"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/CreateRA/SubmitLevel1RAModal.test.tsx">
        <testCase name="SubmitLevel1RAModal renders modal when show is true" duration="38"/>
        <testCase name="SubmitLevel1RAModal does not render modal when show is false" duration="5"/>
        <testCase name="SubmitLevel1RAModal renders all required elements" duration="14"/>
        <testCase name="SubmitLevel1RAModal calls onClose when Cancel button is clicked" duration="8"/>
        <testCase name="SubmitLevel1RAModal calls onClose when close button is clicked" duration="33"/>
        <testCase name="SubmitLevel1RAModal Confirm button is disabled when no approval date is selected" duration="81"/>
        <testCase name="SubmitLevel1RAModal calls onConfirm when Confirm button is clicked with approval date" duration="13"/>
        <testCase name="SubmitLevel1RAModal updates form when date is changed" duration="11"/>
        <testCase name="SubmitLevel1RAModal enables Confirm button when approval date is selected" duration="6"/>
        <testCase name="SubmitLevel1RAModal calls setForm with previous state and approval_date when date changes" duration="33"/>
        <testCase name="SubmitLevel1RAModal clears approval date when empty value is set" duration="37"/>
        <testCase name="SubmitLevel1RAModal renders warning message with correct styling" duration="16"/>
        <testCase name="SubmitLevel1RAModal renders CustomDatePicker with correct props" duration="10"/>
        <testCase name="SubmitLevel1RAModal has correct modal structure" duration="7"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/SingleVesselOfficeDropdown.test.tsx">
        <testCase name="SingleVesselOfficeDropdown renders with default placeholder" duration="26"/>
        <testCase name="SingleVesselOfficeDropdown renders with custom placeholder" duration="5"/>
        <testCase name="SingleVesselOfficeDropdown displays selected value" duration="5"/>
        <testCase name="SingleVesselOfficeDropdown opens dropdown on input focus" duration="13"/>
        <testCase name="SingleVesselOfficeDropdown filters options based on search input" duration="29"/>
        <testCase name="SingleVesselOfficeDropdown calls onChange when an option is selected" duration="7"/>
        <testCase name="SingleVesselOfficeDropdown shows error message when isInvalid is true" duration="2"/>
        <testCase name="SingleVesselOfficeDropdown clears selection when clear button is clicked" duration="18"/>
        <testCase name="SingleVesselOfficeDropdown handles keyboard interactions" duration="8"/>
        <testCase name="SingleVesselOfficeDropdown calls onBlur when input loses focus" duration="167"/>
        <testCase name="SingleVesselOfficeDropdown closes dropdown on outside click" duration="5"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/utils/helper.test.ts">
        <testCase name="generateGroupedOptions should generate grouped options with default columns" duration="13"/>
        <testCase name="generateGroupedOptions should use custom columns when provided" duration="1"/>
        <testCase name="generateGroupedOptions should handle empty input array" duration="1"/>
        <testCase name="generateGroupedOptions should handle group with empty parameters array" duration="0"/>
        <testCase name="generateGroupedOptions should handle group names with special characters" duration="0"/>
        <testCase name="generateGroupedOptions should handle zero columns" duration="1"/>
        <testCase name="removeAndReindexJobState should remove item at index and reindex remaining items" duration="0"/>
        <testCase name="removeAndReindexJobState should remove first item and reindex correctly" duration="0"/>
        <testCase name="removeAndReindexJobState should remove last item and keep other indices unchanged" duration="1"/>
        <testCase name="removeAndReindexJobState should handle empty state object" duration="0"/>
        <testCase name="removeAndReindexJobState should handle single item removal" duration="1"/>
        <testCase name="removeAndReindexJobState should handle non-existent index gracefully" duration="1"/>
        <testCase name="removeAndReindexJobState should handle non-sequential indices" duration="0"/>
        <testCase name="removeAndReindexJobState should preserve object properties and structure" duration="0"/>
        <testCase name="formParameterHandler should delete template_hazard.value when template_hazard[0].isOther is false and value is empty" duration="4"/>
        <testCase name="formParameterHandler should keep template_hazard.value when template_hazard[0].isOther is true" duration="0"/>
        <testCase name="formParameterHandler should keep template_hazard.value when value is not empty" duration="1"/>
        <testCase name="formParameterHandler should filter parameters based on parameter_id length and is_other flag" duration="1"/>
        <testCase name="formParameterHandler should handle undefined parameters array" duration="1"/>
        <testCase name="formParameterHandler should process template_job array and remove job_id when array exists" duration="1"/>
        <testCase name="formParameterHandler should delete template_job when it is not an array and has no job_step" duration="1"/>
        <testCase name="formParameterHandler should process template_task_reliability_assessment when array has length" duration="2"/>
        <testCase name="formParameterHandler should handle template_task_reliability_assessment with missing properties" duration="5"/>
        <testCase name="formParameterHandler should remove value property from parameters when is_other is false" duration="1"/>
        <testCase name="formParameterHandler should handle complex payload with all features" duration="14"/>
        <testCase name="formParameterHandler should handle null and undefined values gracefully" duration="1"/>
        <testCase name="formParameterHandler should handle empty arrays" duration="4"/>
        <testCase name="formParameterHandler should handle parameters with null values" duration="9"/>
        <testCase name="formParameterHandler should delete template_hazard when hazard_id is empty and is_other is false" duration="0"/>
        <testCase name="formParameterHandler should handle undefined template_job gracefully" duration="2"/>
        <testCase name="formParameterHandler should handle template_job with risk ratings correctly" duration="1"/>
        <testCase name="formParameterHandler should delete template_category when category_id is empty" duration="0"/>
        <testCase name="formParameterHandler should keep template_category when category_id has values" duration="0"/>
        <testCase name="formParameterHandler should handle template_job deletion when first job has empty job_step" duration="0"/>
        <testCase name="formParameterHandler should handle risk_category deletion and value handling" duration="0"/>
        <testCase name="formParameterHandler should handle risk_category value deletion when not other" duration="1"/>
        <testCase name="formParameterHandler should handle template_category value deletion when not other" duration="0"/>
        <testCase name="formParameterHandler should handle risk_hazard value deletion when not other" duration="0"/>
        <testCase name="formParameterHandler should handle risk_job processing" duration="0"/>
        <testCase name="formParameterHandler should handle risk_task_reliability_assessment processing with condition" duration="1"/>
        <testCase name="formParameterHandler should delete various optional fields when empty or falsy" duration="1"/>
        <testCase name="formParameterHandler should handle risk_team_member processing" duration="0"/>
        <testCase name="createFormFromData should create form with default values when no data provided" duration="4"/>
        <testCase name="createFormFromData should create form with provided data values" duration="0"/>
        <testCase name="createFormFromData should handle empty template_category array" duration="1"/>
        <testCase name="createFormFromData should map template_hazards correctly" duration="0"/>
        <testCase name="createFormFromData should handle template_hazards without other values" duration="0"/>
        <testCase name="createFormFromData should handle template_parameter mapping" duration="5"/>
        <testCase name="createFormFromData should handle template_job with existing data" duration="0"/>
        <testCase name="createFormFromData should handle template_task_reliability_assessment" duration="1"/>
        <testCase name="createFormFromData should handle template_keyword" duration="0"/>
        <testCase name="createFormFromData should handle audit fields" duration="0"/>
        <testCase name="validateRequiredField should return true for empty string" duration="0"/>
        <testCase name="validateRequiredField should return true for string with only whitespace" duration="1"/>
        <testCase name="validateRequiredField should return true for undefined" duration="0"/>
        <testCase name="validateRequiredField should return false for non-empty string" duration="0"/>
        <testCase name="validateTaskReliabilityAnswers should return true for empty array" duration="0"/>
        <testCase name="validateTaskReliabilityAnswers should return true when any assessment has no answer" duration="0"/>
        <testCase name="validateTaskReliabilityAnswers should return true when any assessment has undefined answer" duration="1"/>
        <testCase name="validateTaskReliabilityAnswers should return false when all assessments have answers" duration="0"/>
        <testCase name="getAssessmentArray should return template_task_reliability_assessment for TemplateForm" duration="0"/>
        <testCase name="getAssessmentArray should return empty array for TemplateForm with non-array assessment" duration="0"/>
        <testCase name="getAssessmentArray should return risk_task_reliability_assessment for RiskForm" duration="0"/>
        <testCase name="getAssessmentArray should return empty array for RiskForm with non-array assessment" duration="1"/>
        <testCase name="getRiskRatingBackgroundColor should return correct color for High rating" duration="0"/>
        <testCase name="getRiskRatingBackgroundColor should return correct color for Medium rating" duration="0"/>
        <testCase name="getRiskRatingBackgroundColor should return correct color for Low rating" duration="0"/>
        <testCase name="getRiskRatingBackgroundColor should return Low color for unknown rating" duration="0"/>
        <testCase name="getRiskRatingTextColor should return correct color for High rating" duration="0"/>
        <testCase name="getRiskRatingTextColor should return correct color for Medium rating" duration="0"/>
        <testCase name="getRiskRatingTextColor should return correct color for Low rating" duration="1"/>
        <testCase name="getRiskRatingTextColor should return Low color for unknown rating" duration="0"/>
        <testCase name="formatDateToYYYYMMDD should format date correctly" duration="0"/>
        <testCase name="formatDateToYYYYMMDD should pad single digit month and day with zeros" duration="0"/>
        <testCase name="formatDateToYYYYMMDD should handle leap year correctly" duration="0"/>
        <testCase name="formatDateToYYYYMMDD should handle year boundaries correctly" duration="1"/>
        <testCase name="calculateRiskRating should return High when any assessment answer is No for TemplateForm" duration="0"/>
        <testCase name="calculateRiskRating should return High when any assessment answer is No for RiskForm" duration="0"/>
        <testCase name="calculateRiskRating should calculate rating based on highest residual risk rating for TemplateForm" duration="1"/>
        <testCase name="calculateRiskRating should calculate rating based on highest residual risk rating for RiskForm" duration="0"/>
        <testCase name="calculateRiskRating should return Medium as default when no ratings found" duration="0"/>
        <testCase name="calculateRiskRating should handle empty assessments and jobs" duration="0"/>
        <testCase name="calculateRiskRating should handle non-array assessments" duration="0"/>
        <testCase name="calculateRiskRating should handle ratings not found in risk matrix" duration="1"/>
        <testCase name="createRiskFormFromData should create risk form with default values when no data provided" duration="6">
            <failure message="Error: expect(received).toEqual(expected) // deep equality"><![CDATA[Error: expect(received).toEqual(expected) // deep equality

- Expected  - 2
+ Received  + 4

@@ -1,18 +1,20 @@
- ObjectContaining {
+ Object {
    "approval_date": undefined,
    "approval_required": Array [],
    "assessor": undefined,
    "created_by": undefined,
    "date_risk_assessment": undefined,
+   "office_id": 0,
+   "office_name": "",
    "parameters": Array [],
    "ra_level": undefined,
    "recovery_measures": "",
    "risk_approver": Array [],
    "risk_category": Object {
      "category_id": Array [],
-     "is_other": false,
+     "is_other": "",
      "value": "",
    },
    "risk_hazard": Object {
      "hazard_id": Array [],
      "is_other": false,
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/utils/helper.test.ts:1533:20)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at Object.worker (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/testWorker.js:133:12)]]></failure>
        </testCase>
        <testCase name="createRiskFormFromData should create risk form with provided data values" duration="2"/>
        <testCase name="createRiskFormFromData should handle risk_approval_required mapping" duration="0"/>
        <testCase name="createRiskFormFromData should handle empty risk_approval_required" duration="0"/>
        <testCase name="transformTemplateToRisk should transform template to risk form with default values" duration="0"/>
        <testCase name="transformTemplateToRisk should transform template category correctly" duration="0"/>
        <testCase name="transformTemplateToRisk should transform template hazards correctly" duration="1"/>
        <testCase name="transformTemplateToRisk should transform template parameters correctly" duration="0"/>
        <testCase name="transformTemplateToRisk should transform template jobs correctly" duration="0"/>
        <testCase name="transformTemplateToRisk should handle missing job properties with defaults" duration="0"/>
        <testCase name="transformTemplateToRisk should transform task reliability assessment correctly" duration="0"/>
        <testCase name="transformTemplateToRisk should handle provided basic fields" duration="1"/>
        <testCase name="Additional coverage tests for uncovered lines formParameterHandler - risk_hazard value deletion should delete risk_hazard.value when is_other is false and value is empty (line 182)" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines createRiskFormFromData - mapRiskCategory fallback (line 508) should handle mapRiskCategory with undefined input that causes map to fail" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines createRiskFormFromData - mapRiskJob array processing (line 530) should process risk_job array with map function" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines createRiskFormFromData - mapRiskTaskReliabilityAssessment array processing (line 547) should process risk_task_reliability_assessment array with map function" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should handle mapRiskCategory with null input to trigger fallback" duration="1">
            <failure message="Error: expect(received).toEqual(expected) // deep equality"><![CDATA[Error: expect(received).toEqual(expected) // deep equality

- Expected  - 1
+ Received  + 1

  Object {
    "category_id": Array [],
-   "is_other": false,
+   "is_other": "",
    "value": "",
  }
    at Object.<anonymous> (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/utils/helper.test.ts:1995:36)
    at Promise.then.completed (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:391:28)
    at new Promise (<anonymous>)
    at callAsyncCircusFn (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/utils.js:316:10)
    at _callCircusTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:218:40)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at _runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:155:3)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:66:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at _runTestsForDescribeBlock (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:60:9)
    at run (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/run.js:25:3)
    at runAndTransformResultsToJestFormat (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapterInit.js:170:21)
    at jestAdapter (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-circus/build/legacy-code-todo-rewrite/jestAdapter.js:82:19)
    at runTestInternal (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:389:16)
    at runTest (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/runTest.js:475:34)
    at Object.worker (/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/node_modules/jest-runner/build/testWorker.js:133:12)]]></failure>
        </testCase>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should handle formParameterHandler risk_category array with isOther check" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should handle template_category array with isOther check" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should handle risk_hazard array with isOther check" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should handle template_hazard array with isOther check" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should handle template_task_reliability_assessment with condition logic" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should handle risk_task_reliability_assessment with condition logic and Yes override" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should handle parameters with null parameter and unique parameter_id" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should handle mapParameters edge cases" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should handle parameters with missing parameterType.id" duration="1"/>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should handle transformTemplateToRisk with missing parameterType.id" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should handle template_hazards filter logic" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should handle calculateRiskRating Low rating calculation" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should handle calculateRiskRating with missing rating in risk matrix" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should handle formParameterHandler with typo in task_reliability_assessmen" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should handle risk_task_reliability_assessment with typo in task_reliability_assessmen" duration="0"/>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should cover line 182 - delete risk_hazard when conditions are met" duration="1"/>
        <testCase name="Additional coverage tests for uncovered lines Additional branch coverage tests should handle various conditional branches for complete coverage" duration="0"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/CreateRA/ReAssignApproverModal.test.tsx">
        <testCase name="ReAssignApproverModal opens modal on trigger click and closes on cancel" duration="86"/>
        <testCase name="ReAssignApproverModal disables Confirm button until a user is selected" duration="19"/>
        <testCase name="ReAssignApproverModal calls onConfirm with selected user and order, disables buttons while loading, and closes modal" duration="126"/>
        <testCase name="ReAssignApproverModal resets selectedUser on close" duration="22"/>
        <testCase name="ReAssignApproverModal handles error in onConfirm gracefully and closes modal" duration="75"/>
        <testCase name="ReAssignApproverModal does not break if trigger is null" duration="20"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/ProjectBreadCrumb.test.tsx">
        <testCase name="ProjectBreadCrumb renders breadcrumb without links when none are provided" duration="19"/>
        <testCase name="ProjectBreadCrumb applies correct text and link styles" duration="9"/>
        <testCase name="ProjectBreadCrumb preserves state in the link if provided" duration="4"/>
        <testCase name="ProjectBreadCrumb renders correctly with only one item" duration="2"/>
        <testCase name="ProjectBreadCrumb handles onClick events correctly" duration="10"/>
        <testCase name="ProjectBreadCrumb renders separator between multiple items correctly" duration="1"/>
        <testCase name="ProjectBreadCrumb renders custom options correctly" duration="6"/>
        <testCase name="ProjectBreadCrumb renders without options when not provided" duration="2"/>
        <testCase name="ProjectBreadCrumb handles items with both link and state correctly" duration="2"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/CreateRA/RaCategoryStep.test.tsx">
        <testCase name="RaCategoryStep Component renders correctly with default props" duration="22"/>
        <testCase name="RaCategoryStep Component passes correct risk category list from data store" duration="6"/>
        <testCase name="RaCategoryStep Component handles empty risk category list gracefully" duration="5"/>
        <testCase name="RaCategoryStep Component handles null risk category list gracefully" duration="3"/>
        <testCase name="RaCategoryStep Component passes initial checked categories correctly" duration="2"/>
        <testCase name="RaCategoryStep Component passes others selection state correctly" duration="3"/>
        <testCase name="RaCategoryStep Component handles category selection changes correctly" duration="6"/>
        <testCase name="RaCategoryStep Component handles others selection changes correctly" duration="7"/>
        <testCase name="RaCategoryStep Component clears others text when others is deselected" duration="3"/>
        <testCase name="RaCategoryStep Component validates correctly when no categories are selected" duration="3"/>
        <testCase name="RaCategoryStep Component validates correctly when categories are selected" duration="2"/>
        <testCase name="RaCategoryStep Component validates correctly when others is selected with text" duration="2"/>
        <testCase name="RaCategoryStep Component validates correctly when others is selected but text is empty" duration="5"/>
        <testCase name="RaCategoryStep Component validates correctly when others has only whitespace" duration="2"/>
        <testCase name="RaCategoryStep Component validates correctly when both categories and others are selected" duration="2"/>
        <testCase name="RaCategoryStep Component calls onValidate on form changes" duration="3"/>
        <testCase name="RaCategoryStep Component works without onValidate callback" duration="2"/>
        <testCase name="RaCategoryStep Component handles undefined template_category gracefully" duration="3"/>
        <testCase name="RaCategoryStep Component handles empty task_requiring_ra gracefully" duration="1"/>
        <testCase name="RaCategoryStep Component validates on component mount" duration="2"/>
        <testCase name="RaCategoryStep Component re-validates when form.template_category.category_id changes" duration="2"/>
        <testCase name="RaCategoryStep Component re-validates when form.template_category changes" duration="3"/>
        <testCase name="RaCategoryStep Component does not re-validate when other form properties change" duration="2"/>
        <testCase name="RaCategoryStep Component Risk Form Tests renders correctly with risk form type" duration="3"/>
        <testCase name="RaCategoryStep Component Risk Form Tests passes initial checked categories correctly for risk form" duration="2"/>
        <testCase name="RaCategoryStep Component Risk Form Tests passes others selection state correctly for risk form" duration="3"/>
        <testCase name="RaCategoryStep Component Risk Form Tests handles category selection changes correctly for risk form" duration="4"/>
        <testCase name="RaCategoryStep Component Risk Form Tests handles others selection changes correctly for risk form" duration="2"/>
        <testCase name="RaCategoryStep Component Risk Form Tests validates correctly when no categories are selected for risk form" duration="5"/>
        <testCase name="RaCategoryStep Component Risk Form Tests validates correctly when categories are selected for risk form" duration="2"/>
        <testCase name="RaCategoryStep Component Risk Form Tests re-validates when risk_category.category_id changes" duration="1"/>
        <testCase name="RaCategoryStep Component Risk Form Tests handles undefined risk_category gracefully" duration="1"/>
        <testCase name="RaCategoryStep Component Risk Form Tests handles missing date_risk_assessment gracefully" duration="1"/>
        <testCase name="RaCategoryStep Component isEdit prop tests passes isEdit prop correctly when true" duration="2"/>
        <testCase name="RaCategoryStep Component isEdit prop tests passes isEdit prop correctly when false" duration="2"/>
        <testCase name="RaCategoryStep Component isEdit prop tests defaults isEdit to false when not provided" duration="2"/>
        <testCase name="RaCategoryStep Component Edge cases and helper function coverage handles form with null task_requiring_ra" duration="2"/>
        <testCase name="RaCategoryStep Component Edge cases and helper function coverage handles form with undefined task_requiring_ra" duration="2"/>
        <testCase name="RaCategoryStep Component Edge cases and helper function coverage handles clearing others value when flag is false" duration="4"/>
        <testCase name="RaCategoryStep Component Edge cases and helper function coverage handles risk form with missing date_risk_assessment field" duration="2"/>
        <testCase name="RaCategoryStep Component Edge cases and helper function coverage handles template form with missing template_category field" duration="2"/>
        <testCase name="RaCategoryStep Component Edge cases and helper function coverage handles risk form with missing risk_category field" duration="2"/>
        <testCase name="RaCategoryStep Component Edge cases and helper function coverage handles form with partial template_category data" duration="2"/>
        <testCase name="RaCategoryStep Component Edge cases and helper function coverage handles form with partial risk_category data" duration="0"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/routes/rootRoutes.test.tsx">
        <testCase name="AppRoutes Basic Functionality renders without crashing" duration="19"/>
        <testCase name="AppRoutes Basic Functionality renders for unknown routes" duration="1"/>
        <testCase name="AppRoutes Basic Functionality passes roleConfig to routesConfig function" duration="9"/>
        <testCase name="AppRoutes RouteGenerator Coverage covers isPermission false branch" duration="2"/>
        <testCase name="AppRoutes RouteGenerator Coverage covers redirect branch" duration="2"/>
        <testCase name="AppRoutes RouteGenerator Coverage covers component with childRoutes branch" duration="1"/>
        <testCase name="AppRoutes RouteGenerator Coverage covers component without childRoutes branch" duration="1"/>
        <testCase name="AppRoutes RouteGenerator Coverage covers route without component or redirect" duration="1"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/Link.test.tsx">
        <testCase name="Link renders children and href" duration="17"/>
        <testCase name="Link calls navigate on click" duration="7"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/Loader.test.tsx">
        <testCase name="Loader renders with default props" duration="27"/>
        <testCase name="Loader renders with overlay loader class when isOverlayLoader is true" duration="1"/>
        <testCase name="Loader renders without overlay loader class when isOverlayLoader is false" duration="2"/>
        <testCase name="Loader applies custom className when provided" duration="3"/>
        <testCase name="Loader applies both custom className and overlay loader class" duration="1"/>
        <testCase name="Loader has correct default classes" duration="2"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/CreateRA/HazardCategoryStep.test.tsx">
        <testCase name="HazardCategoryStep Component renders correctly with default props" duration="39"/>
        <testCase name="HazardCategoryStep Component passes correct hazards list from data store" duration="7"/>
        <testCase name="HazardCategoryStep Component handles empty hazards list gracefully" duration="6"/>
        <testCase name="HazardCategoryStep Component passes initial checked hazards correctly" duration="4"/>
        <testCase name="HazardCategoryStep Component passes others selection state correctly" duration="17"/>
        <testCase name="HazardCategoryStep Component handles hazard selection changes correctly" duration="35"/>
        <testCase name="HazardCategoryStep Component handles others selection changes correctly" duration="2"/>
        <testCase name="HazardCategoryStep Component clears others text when others is deselected" duration="2"/>
        <testCase name="HazardCategoryStep Component validates correctly when no hazards are selected" duration="1"/>
        <testCase name="HazardCategoryStep Component validates correctly when hazards are selected" duration="21"/>
        <testCase name="HazardCategoryStep Component validates correctly when others is selected with text" duration="16"/>
        <testCase name="HazardCategoryStep Component validates correctly when others is selected but text is empty" duration="6"/>
        <testCase name="HazardCategoryStep Component validates correctly when others has only whitespace" duration="3"/>
        <testCase name="HazardCategoryStep Component validates correctly when both hazards and others are selected" duration="2"/>
        <testCase name="HazardCategoryStep Component calls onValidate on form changes" duration="4"/>
        <testCase name="HazardCategoryStep Component works without onValidate callback" duration="2"/>
        <testCase name="HazardCategoryStep Component handles undefined template_hazard gracefully" duration="4"/>
        <testCase name="HazardCategoryStep Component handles missing task_requiring_ra gracefully" duration="3"/>
        <testCase name="HazardCategoryStep Component exposes validate method through ref" duration="3"/>
        <testCase name="HazardCategoryStep Component validates on component mount" duration="2"/>
        <testCase name="HazardCategoryStep Component Risk Form Type renders correctly with risk form type" duration="21"/>
        <testCase name="HazardCategoryStep Component Risk Form Type handles risk form hazard selection changes correctly" duration="4"/>
        <testCase name="HazardCategoryStep Component Risk Form Type handles risk form others selection changes correctly" duration="3"/>
        <testCase name="HazardCategoryStep Component Risk Form Type handles risk form with pre-selected hazards" duration="1"/>
        <testCase name="HazardCategoryStep Component Risk Form Type handles risk form with others selected and text" duration="1"/>
        <testCase name="HazardCategoryStep Component Risk Form Type validates risk form correctly when hazards are selected" duration="1"/>
        <testCase name="HazardCategoryStep Component Risk Form Type validates risk form correctly when others is selected with text" duration="3"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/CreateRA/AtRiskStep.test.tsx">
        <testCase name="AtRiskStep Component renders correctly with default props" duration="28"/>
        <testCase name="AtRiskStep Component calls generateGroupedOptions with correct parameters" duration="6"/>
        <testCase name="AtRiskStep Component passes correct props to GroupedCheckboxGrid" duration="4"/>
        <testCase name="AtRiskStep Component handles form changes correctly" duration="7"/>
        <testCase name="AtRiskStep Component validates correctly when no parameters are selected" duration="4"/>
        <testCase name="AtRiskStep Component validates correctly when parameters are selected" duration="3"/>
        <testCase name="AtRiskStep Component validates correctly when others option is selected with text" duration="1"/>
        <testCase name="AtRiskStep Component validates correctly when others option is selected but text is empty" duration="2"/>
        <testCase name="AtRiskStep Component validates correctly when others option has only whitespace" duration="5"/>
        <testCase name="AtRiskStep Component handles mixed validation scenarios correctly" duration="12"/>
        <testCase name="AtRiskStep Component handles empty parameters array correctly" duration="3"/>
        <testCase name="AtRiskStep Component handles non-array parameters correctly" duration="2"/>
        <testCase name="AtRiskStep Component calls onValidate on form parameter changes" duration="2"/>
        <testCase name="AtRiskStep Component works without onValidate callback" duration="1"/>
        <testCase name="AtRiskStep Component handles empty task_requiring_ra correctly" duration="1"/>
        <testCase name="AtRiskStep Component handles undefined task_requiring_ra correctly" duration="1"/>
        <testCase name="AtRiskStep Component handles others change correctly" duration="3"/>
        <testCase name="AtRiskStep Component exposes validate method through ref" duration="2"/>
        <testCase name="AtRiskStep Component handles complex parameter validation scenarios" duration="1"/>
        <testCase name="AtRiskStep Component handles validation with null parameter_id arrays" duration="1"/>
        <testCase name="AtRiskStep Component handles validation with undefined parameter_id arrays" duration="0"/>
        <testCase name="AtRiskStep Component handles validation when riskParameterType is empty" duration="6"/>
        <testCase name="AtRiskStep Component handles validation when riskParameterType is undefined" duration="1"/>
        <testCase name="AtRiskStep Component validates correctly with correct parameters" duration="0"/>
        <testCase name="AtRiskStep Component handles form updates correctly when setForm is called multiple times" duration="5"/>
        <testCase name="AtRiskStep Component maintains component stability when props change" duration="3"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/GenericStepper.test.tsx">
        <testCase name="GenericStepper renders first step content and breadcrumb title" duration="34"/>
        <testCase name="GenericStepper calls onClose when close icon is clicked" duration="15"/>
        <testCase name="GenericStepper navigates to next step on Next click and calls onNext" duration="10"/>
        <testCase name="GenericStepper calls primaryBtnOnClick on final step" duration="12"/>
        <testCase name="GenericStepper calls secondaryBtnOnClick when Cancel is clicked" duration="2"/>
        <testCase name="GenericStepper disables buttons when disabled props are passed" duration="2"/>
        <testCase name="GenericStepper supports function-based primary button title" duration="9"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RATemplateListing/TemplateView.test.tsx">
        <testCase name="TemplateView Component Rendering renders loading state initially" duration="39"/>
        <testCase name="TemplateView Component Rendering renders template data after successful fetch" duration="16"/>
        <testCase name="TemplateView Component Rendering renders breadcrumb options correctly" duration="6"/>
        <testCase name="TemplateView Data Fetching calls createFormFromData with template data" duration="5"/>
        <testCase name="TemplateView Data Fetching calls getTemplateById when id is provided" duration="21"/>
        <testCase name="TemplateView Error Handling handles API error gracefully" duration="2"/>
        <testCase name="TemplateView Error Handling continues rendering even after fetch error" duration="5"/>
        <testCase name="TemplateView Loading States shows loader during initial render" duration="1"/>
        <testCase name="TemplateView Loading States hides loader after successful data fetch" duration="3"/>
        <testCase name="TemplateView Loading States hides loader after failed data fetch" duration="4"/>
        <testCase name="TemplateView PreviewFormDetails Props passes correct props to PreviewFormDetails" duration="5"/>
        <testCase name="TemplateView PreviewFormDetails Props handles empty task_requiring_ra in breadcrumb" duration="2"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/utils/common.test.ts">
        <testCase name="cleanObject removes keys with null or undefined values" duration="2"/>
        <testCase name="cleanObject removes keys with empty arrays" duration="1"/>
        <testCase name="cleanObject removes nested empty objects recursively" duration="0"/>
        <testCase name="cleanObject keeps nested objects with non-empty keys" duration="0"/>
        <testCase name="cleanObject returns empty object if all values are empty or null" duration="0"/>
        <testCase name="parseDate returns formatted date string for valid date input" duration="9"/>
        <testCase name="parseDate returns undefined for invalid date string" duration="73"/>
        <testCase name="parseDate returns undefined when date is null or undefined" duration="1"/>
        <testCase name="parseDate formats date with custom format string" duration="2"/>
        <testCase name="getDateRangeFilters returns empty object if no range is provided" duration="1"/>
        <testCase name="getDateRangeFilters returns only start_date filter if only start date is provided" duration="0"/>
        <testCase name="getDateRangeFilters returns only end_date filter if only end date is provided" duration="1"/>
        <testCase name="getDateRangeFilters returns both start_date and end_date filters if both provided" duration="0"/>
        <testCase name="withDefault returns original value if not empty" duration="2"/>
        <testCase name="withDefault returns default placeholder for empty values" duration="1"/>
        <testCase name="withDefault uses custom placeholder when provided" duration="0"/>
        <testCase name="getRaApprovalStatus returns correct status and color for approved status" duration="1"/>
        <testCase name="getRaApprovalStatus returns correct status and color for rejected status" duration="0"/>
        <testCase name="getRaApprovalStatus returns pending status for other statuses" duration="1"/>
        <testCase name="getRiskRatingStatus returns correct status and color for high risk" duration="1"/>
        <testCase name="getRiskRatingStatus returns correct status and color for medium risk" duration="0"/>
        <testCase name="getRiskRatingStatus returns correct status and color for low risk" duration="0"/>
        <testCase name="getRiskRatingStatus returns null values for unknown risk status" duration="1"/>
        <testCase name="getRiskRatingValue returns correct enum value for valid risk rating strings" duration="1"/>
        <testCase name="getRiskRatingValue handles case-insensitive input" duration="0"/>
        <testCase name="getRiskRatingValue handles whitespace in input" duration="0"/>
        <testCase name="getRiskRatingValue returns undefined for invalid risk ratings" duration="12"/>
        <testCase name="compareTemplateItems with CategoryItems returns true when all items match by ID" duration="1"/>
        <testCase name="compareTemplateItems with CategoryItems returns true when other category items match by value" duration="0"/>
        <testCase name="compareTemplateItems with CategoryItems returns false when lengths are different" duration="1"/>
        <testCase name="compareTemplateItems with CategoryItems returns false when items do not match" duration="0"/>
        <testCase name="compareTemplateItems with HazardItems returns true when all hazard items match by ID" duration="0"/>
        <testCase name="compareTemplateItems with HazardItems returns true when other hazard items match by value" duration="0"/>
        <testCase name="compareTemplateItems handles undefined or empty arrays" duration="0"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/icons/trash-icon.test.tsx">
        <testCase name="TrashIcon renders with default props" duration="112"/>
        <testCase name="TrashIcon renders with custom width, height, and color" duration="9"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/Stepper.test.tsx">
        <testCase name="Stepper Component renders the correct number of steps" duration="35"/>
        <testCase name="Stepper Component displays the correct labels for each step" duration="20"/>
        <testCase name="Stepper Component highlights the current active step" duration="16"/>
        <testCase name="Stepper Component shows checkmark for completed steps" duration="6"/>
        <testCase name="Stepper Component shows empty circle for incomplete steps" duration="41"/>
        <testCase name="Stepper Component allows clicking on completed steps" duration="171"/>
        <testCase name="Stepper Component does not allow clicking on current or future steps" duration="13"/>
        <testCase name="Stepper Component applies correct cursor style based on step state" duration="23"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RATemplateListing/components/TemplateListingHeader.test.tsx">
        <testCase name="TemplateListingHeader renders breadcrumb and create new button" duration="86"/>
        <testCase name="TemplateListingHeader does not render create new button if permission is false" duration="2"/>
        <testCase name="TemplateListingHeader navigates to /risk-assessment when breadcrumb button is clicked" duration="10"/>
        <testCase name="TemplateListingHeader navigates to /risk-assessment/templates/create when create new is clicked" duration="4"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/context/AlertContextProvider.test.tsx">
        <testCase name="AlertContextProvider renders children correctly" duration="47"/>
        <testCase name="AlertContextProvider shows an alert when alert() is called" duration="76"/>
        <testCase name="AlertContextProvider alertDetails is null initially" duration="2"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/TemplateSelection/components/TemplateSelectionFilters.test.tsx">
        <testCase name="TemplateSelectionFilter renders search input and categories drawer" duration="43"/>
        <testCase name="TemplateSelectionFilter calls onFilterChange for search" duration="8"/>
        <testCase name="TemplateSelectionFilter calls onFilterChange for RA and Hazard categories" duration="4"/>
        <testCase name="TemplateSelectionFilter shows toast on data load error" duration="54"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/pages/RAListing/components/RAFilters.test.tsx">
        <testCase name="RAFilters renders all filter controls and more filters drawer" duration="90"/>
        <testCase name="RAFilters calls onFilterChange for vessel and office dropdown" duration="38"/>
        <testCase name="RAFilters calls onFilterChange for date picker with various date scenarios" duration="64"/>
        <testCase name="RAFilters renders date picker with existing dates" duration="14"/>
        <testCase name="RAFilters calls onFilterChange for search dropdowns" duration="7"/>
        <testCase name="RAFilters tests getRaBasicFiltersFormConfig with various filter states" duration="24"/>
        <testCase name="RAFilters handles async error in useEffect and shows toast" duration="33"/>
        <testCase name="RAFilters successfully loads and sets vessel, office, and category data" duration="10"/>
        <testCase name="RAFilters tests getVesselOfficeOptions function indirectly through component" duration="19"/>
        <testCase name="RAFilters handles dropdown filter changes with empty values" duration="10"/>
        <testCase name="RAFilters tests date range filter with null values" duration="5"/>
        <testCase name="RAFilters exports raFiltersInitialState correctly" duration="0"/>
        <testCase name="RAFilters tests useMemo dependency changes" duration="3"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/ExitCreateRiskPageModal.test.tsx">
        <testCase name="ExitCreateRiskPageModal should render modal with correct content" duration="47"/>
        <testCase name="ExitCreateRiskPageModal should call handleSubmit when &quot;Discard RA&quot; button is clicked" duration="9"/>
        <testCase name="ExitCreateRiskPageModal should call onClose when &quot;Keep Editing&quot; button is clicked" duration="7"/>
        <testCase name="ExitCreateRiskPageModal should have correct data-testid for error alert" duration="17"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/TruncateBasicText.test.tsx">
        <testCase name="TruncateText renders full text when text length is within maxLength" duration="29"/>
        <testCase name="TruncateText renders truncated text when text length exceeds maxLength" duration="2"/>
        <testCase name="TruncateText does not show popover if text is not truncated" duration="30"/>
        <testCase name="TruncateText shows full text in popover on hover when text is truncated" duration="184"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/services/http-service.test.ts">
        <testCase name="httpService Basic exports should export HttpMethods with correct values" duration="1"/>
        <testCase name="httpService Basic exports should return an axios instance from getAxiosClient" duration="0"/>
        <testCase name="httpService Basic exports should export the axios module" duration="26"/>
        <testCase name="httpService Basic exports should export cancelPreviousRequest function" duration="2"/>
        <testCase name="httpService Axios instance configuration should create axios instance with interceptors" duration="2"/>
        <testCase name="httpService Axios instance configuration should have interceptor methods available" duration="2"/>
        <testCase name="httpService Axios instance configuration should return same instance on multiple calls" duration="0"/>
        <testCase name="httpService Error handling utilities should identify cancellation errors correctly" duration="22"/>
        <testCase name="httpService Error handling utilities should identify non-cancellation errors correctly" duration="26"/>
        <testCase name="httpService cancelPreviousRequest should create new controller for first request to endpoint" duration="1"/>
        <testCase name="httpService cancelPreviousRequest should cancel previous request and create new controller for same endpoint" duration="0"/>
        <testCase name="httpService cancelPreviousRequest should handle multiple different endpoints independently" duration="1"/>
        <testCase name="httpService cancelPreviousRequest should only cancel controller for specific endpoint" duration="0"/>
        <testCase name="httpService cancelPreviousRequest should handle empty endpoint string" duration="12"/>
        <testCase name="httpService cancelPreviousRequest should handle special characters in endpoint names" duration="1"/>
        <testCase name="httpService Integration Tests should maintain separate axios instance from global axios" duration="1"/>
        <testCase name="httpService Integration Tests should have all required methods on axios instance" duration="2"/>
        <testCase name="httpService Module structure should export all required properties" duration="1"/>
        <testCase name="httpService Module structure should have HttpMethods as an object with string values" duration="1"/>
        <testCase name="httpService Module structure should have getAxiosClient as a function" duration="0"/>
        <testCase name="httpService Module structure should have cancelPreviousRequest as a function" duration="1"/>
        <testCase name="httpService Interceptor functionality verification should have interceptors configured on axios instance" duration="1"/>
        <testCase name="httpService Interceptor functionality verification should handle axios isCancel method correctly" duration="4"/>
        <testCase name="httpService Edge cases and error scenarios should handle multiple calls to getAxiosClient" duration="0"/>
        <testCase name="httpService Edge cases and error scenarios should handle cancelPreviousRequest with various endpoint formats" duration="3"/>
        <testCase name="httpService Edge cases and error scenarios should maintain separate controllers for different endpoints" duration="1"/>
        <testCase name="httpService httpService uncovered/error branches request interceptor adds Authorization if token present" duration="4"/>
        <testCase name="httpService httpService uncovered/error branches request interceptor leaves headers if no token" duration="5"/>
        <testCase name="httpService httpService uncovered/error branches request interceptor passes through error" duration="2"/>
        <testCase name="httpService httpService uncovered/error branches response interceptor returns response" duration="2"/>
        <testCase name="httpService httpService uncovered/error branches response interceptor handles axios.isCancel" duration="3"/>
        <testCase name="httpService httpService uncovered/error branches response interceptor passes through non-cancel errors" duration="4"/>
        <testCase name="httpService httpService uncovered/error branches cancelPreviousRequest aborts and replaces controllers" duration="4"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/paris2-risk-assessment.test.tsx">
        <testCase name="paris2-risk-assessment lifecycles should export bootstrap, mount, and unmount lifecycle functions" duration="3"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/CardGallery.test.tsx">
        <testCase name="CardGallery renders loading state" duration="15"/>
        <testCase name="CardGallery renders empty state" duration="2"/>
        <testCase name="CardGallery renders items" duration="2"/>
        <testCase name="CardGallery calls fetchNextPage on scroll near bottom" duration="2"/>
        <testCase name="CardGallery shows spinner when fetching next page" duration="4"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/LevelOfRATag.test.tsx">
        <testCase name="LevelOfRATag renders correctly with default content" duration="17"/>
        <testCase name="LevelOfRATag renders with correct structure and classes" duration="2"/>
        <testCase name="LevelOfRATag renders ColoredTile with correct props" duration="1"/>
        <testCase name="LevelOfRATag has correct layout structure" duration="2"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/icons/external-link-icon.test.tsx">
        <testCase name="ExternalLinkIcon renders with default props" duration="20"/>
        <testCase name="ExternalLinkIcon renders with custom size and color" duration="4"/>
        <testCase name="ExternalLinkIcon forwards extra props" duration="1"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/hooks/useContextWrapper.test.tsx">
        <testCase name="useContextWrapper returns context value when inside provider" duration="14"/>
        <testCase name="useContextWrapper throws error when used outside provider" duration="82"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/utils/user.test.ts">
        <testCase name="getInitials returns empty string for undefined" duration="1"/>
        <testCase name="getInitials returns empty string for empty string" duration="1"/>
        <testCase name="getInitials returns first two letters uppercased for single word" duration="0"/>
        <testCase name="getInitials returns initials for two words" duration="0"/>
        <testCase name="getInitials returns initials for more than two words" duration="0"/>
        <testCase name="getInitials handles extra spaces" duration="0"/>
        <testCase name="getInitials handles names with non-letters" duration="1"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/components/icons/calendar-icon.test.tsx">
        <testCase name="CalendarIcon renders with default props" duration="15"/>
        <testCase name="CalendarIcon renders with custom size and color" duration="1"/>
        <testCase name="CalendarIcon forwards extra props" duration="1"/>
        <testCase name="CalendarIcon has correct path element with default styling" duration="1"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/services/services.test.ts">
        <testCase name="Services getRiskCategoryList should fetch risk categories successfully" duration="1"/>
        <testCase name="Services getRiskCategoryList should handle API errors" duration="16"/>
        <testCase name="Services getHazardsList should fetch hazards list successfully" duration="1"/>
        <testCase name="Services getHazardsList should handle API errors" duration="0"/>
        <testCase name="Services getRiskParameterType should fetch risk parameter types successfully" duration="0"/>
        <testCase name="Services getRiskParameterType should handle API errors" duration="1"/>
        <testCase name="Services getMainRiskParameterType should fetch main risk parameter types without filter" duration="0"/>
        <testCase name="Services getMainRiskParameterType should fetch main risk parameter types with filter for risk rating" duration="5"/>
        <testCase name="Services getMainRiskParameterType should handle API errors" duration="0"/>
        <testCase name="Services getTaskReliabilityAssessList should fetch task reliability assessment list successfully" duration="1"/>
        <testCase name="Services getTaskReliabilityAssessList should handle API errors" duration="0"/>
        <testCase name="Services generateQueryParams should generate query params for single value" duration="0"/>
        <testCase name="Services generateQueryParams should generate query params for multiple values" duration="1"/>
        <testCase name="Services generateQueryParams should handle empty array" duration="0"/>
        <testCase name="Services generateQueryParams should encode special characters" duration="1"/>
        <testCase name="Services generateQueryParams should handle values with spaces and special characters" duration="0"/>
        <testCase name="Services GET_VESSEL_USER_DETAILS should generate correct URL with default parameters" duration="0"/>
        <testCase name="Services GET_VESSEL_USER_DETAILS should generate correct URL with custom key and single id" duration="0"/>
        <testCase name="Services GET_VESSEL_USER_DETAILS should generate correct URL with custom key and multiple ids" duration="0"/>
        <testCase name="Services GET_VESSEL_USER_DETAILS should handle empty ids array" duration="1"/>
        <testCase name="Services getTemplateList should fetch template list successfully with basic params" duration="0"/>
        <testCase name="Services getTemplateList should handle API errors" duration="0"/>
        <testCase name="Services getTemplateUserList should fetch template user list successfully" duration="0"/>
        <testCase name="Services getTemplateUserList should handle API errors" duration="1"/>
        <testCase name="Services Uncovered service functions markTemplateAsArchived should PATCH and return data" duration="0"/>
        <testCase name="Services Uncovered service functions getMostlyUsedTemplates should GET and return result" duration="0"/>
        <testCase name="Services Uncovered service functions getRAList should GET with params and signal" duration="0"/>
        <testCase name="Services Uncovered service functions getRAStringOptions should GET and return data" duration="1"/>
        <testCase name="Services Uncovered service functions getTemplateById should GET and return data" duration="0"/>
        <testCase name="Services Uncovered service functions deleteTemplateById should DELETE and return data" duration="1"/>
        <testCase name="Services Uncovered service functions deleteRiskById should DELETE and return data" duration="1"/>
        <testCase name="Services Uncovered service functions createNewTemplate should POST and return data" duration="0"/>
        <testCase name="Services Uncovered service functions updateSavedTemplate should PATCH and return data" duration="1"/>
        <testCase name="Services Uncovered service functions getVesselsList should GET and return data" duration="0"/>
        <testCase name="Services Uncovered service functions getOfficesList should GET and return data" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for markTemplateAsArchived" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for getMostlyUsedTemplates" duration="1"/>
        <testCase name="Services Uncovered service functions should throw on error for getRAList" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for getRAStringOptions" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for getTemplateById" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for deleteTemplateById" duration="1"/>
        <testCase name="Services Uncovered service functions should throw on error for deleteRiskById" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for createNewTemplate" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for updateSavedTemplate" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for getVesselsList" duration="1"/>
        <testCase name="Services Uncovered service functions should throw on error for getOfficesList" duration="0"/>
        <testCase name="Services Uncovered service functions getRiskById should GET and return data" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for getRiskById" duration="0"/>
        <testCase name="Services Uncovered service functions getCrewList should GET with vessel_id param and return crewList" duration="1"/>
        <testCase name="Services Uncovered service functions should throw on error for getCrewList" duration="0"/>
        <testCase name="Services Uncovered service functions createNewRA should POST and return data" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for createNewRA" duration="0"/>
        <testCase name="Services Uncovered service functions updateSavedRA should PATCH and return data" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for updateSavedRA" duration="1"/>
        <testCase name="Services Uncovered service functions getApprovalsRequiredList should GET and return data" duration="0"/>
        <testCase name="Services Uncovered service functions should throw on error for getApprovalsRequiredList" duration="0"/>
        <testCase name="Services Uncovered service functions getSeafarerRanks should GET with ranks query param and return data" duration="3"/>
        <testCase name="Services Uncovered service functions should throw on error for getSeafarerRanks" duration="0"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/utils/error.test.ts">
        <testCase name="extractErrorMessage returns default message when error is falsy or has no message" duration="0"/>
        <testCase name="extractErrorMessage returns error.message if present" duration="0"/>
        <testCase name="extractErrorMessage returns axios error message if isAxiosError is true and message exists" duration="0"/>
        <testCase name="extractErrorMessage returns nested axios response.data.message if present" duration="0"/>
        <testCase name="extractErrorMessage falls back to axios error message if response.data.message is missing" duration="0"/>
        <testCase name="extractErrorMessage handles error that is actually an AxiosError instance" duration="1"/>
    </file>
    <file path="/Users/<USER>/Documents/projects/fml/paris2-web-risk-assessment/__tests__/hooks/useInfiniteQuery.test.tsx">
        <testCase name="useInfiniteQuery fetches initial data and shows loading state" duration="33"/>
        <testCase name="useInfiniteQuery fetches next page and appends data" duration="11"/>
        <testCase name="useInfiniteQuery handles errors on fetch" duration="3"/>
        <testCase name="useInfiniteQuery refetches data when refetch is called" duration="5"/>
        <testCase name="useInfiniteQuery resets to initial page and refetches data when reset is called" duration="8"/>
        <testCase name="useInfiniteQuery handles no next page from the start" duration="3"/>
        <testCase name="useInfiniteQuery prevents fetching next page if already fetching" duration="78"/>
        <testCase name="useInfiniteQuery prevents fetching next page if hasNextPage is false" duration="6"/>
        <testCase name="useInfiniteQuery resets and refetches when options change after mount" duration="12"/>
        <testCase name="useInfiniteQuery handles empty response data gracefully" duration="8"/>
        <testCase name="useInfiniteQuery resets state and data correctly" duration="6"/>
        <testCase name="useInfiniteQuery does not fetch next page when hasNextPage is false" duration="4"/>
        <testCase name="useInfiniteQuery does not fetch next page if already fetching" duration="54"/>
        <testCase name="useInfiniteQuery uses default page and limit values when options are not provided" duration="4"/>
        <testCase name="useInfiniteQuery handles fetch error and sets error state" duration="1"/>
    </file>
</testExecutions>